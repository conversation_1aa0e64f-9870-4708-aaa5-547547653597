# Path to sources
sonar.projectKey=paris2-web-risk-assessment
sonar.projectName=paris2-web-risk-assessment
sonar.sources=src
sonar.exclusions=
# Path to tests
sonar.testExecutionReportPaths=reports/test-report.xml
sonar.tests=.
sonar.test.exclusions=
sonar.test.inclusions=__tests__/**/*.test.tsx

sonar.coverage.exclusions=**/*.test.tsx,**/mocks/**,src/context/DataStoreProvider.tsx,src/constants/**,src/enums/**


# Source encoding
sonar.sourceEncoding=UTF-8

# Exclusions for copy-paste detection
#sonar.cpd.exclusions=

sonar.typescript.tsconfigPath=./tsconfig-sonar.json
sonar.typescript.lcov.reportPaths=__tests__/coverage/lcov.info