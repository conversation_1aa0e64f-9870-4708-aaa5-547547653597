import React from 'react';
import {render, screen} from '@testing-library/react';
import {
  SortIcon,
  RoundCheckFilled,
  CheckFilled,
  CheckUnFilled,
  CommentIcon,
  InfoIcon,
  CalendarIcon,
  DeleteJobIcon,
  JobCardArrowUpIcon,
  JobCardArrowDownIcon,
  EditFormDetailIcon,
  ActionMenuIcon,
  RadioUncheckedIcon,
  RadioCheckedIcon,
} from '../../src/utils/svgIcons';

describe('SVG Icons', () => {
  test('SortIcon renders correctly', () => {
    render(<SortIcon data-testid="sort-icon" />);
    const icon = screen.getByTestId('sort-icon');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 24 24');
  });

  test('CheckFilled renders correctly', () => {
    render(<CheckFilled data-testid="check-filled" />);
    const icon = screen.getByTestId('check-filled');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 20 20');
    expect(icon.querySelector('rect')).toHaveAttribute('fill', '#0091B8');
  });

  test('CheckUnFilled renders correctly', () => {
    render(<CheckUnFilled data-testid="check-unfilled" />);
    const icon = screen.getByTestId('check-unfilled');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('rect')).toHaveAttribute('fill', 'white');
    expect(icon.querySelector('rect')).toHaveAttribute('stroke', '#CCCCCC');
  });

  test('CommentIcon renders correctly', () => {
    render(<CommentIcon data-testid="comment-icon" />);
    const icon = screen.getByTestId('comment-icon');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 20 20');
  });

  test('InfoIcon renders correctly', () => {
    render(<InfoIcon data-testid="info-icon" />);
    const icon = screen.getByTestId('info-icon');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('width', '16');
    expect(icon).toHaveAttribute('height', '16');
  });

  test('CalendarIcon renders correctly', () => {
    render(<CalendarIcon data-testid="calendar-icon" />);
    const icon = screen.getByTestId('calendar-icon');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 14 14');
  });

  test('DeleteJobIcon renders correctly', () => {
    render(<DeleteJobIcon data-testid="delete-job-icon" />);
    const icon = screen.getByTestId('delete-job-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('path')).toHaveAttribute('fill', '#1F4A70');
  });

  test('JobCardArrowUpIcon renders correctly', () => {
    render(<JobCardArrowUpIcon data-testid="arrow-up-icon" />);
    const icon = screen.getByTestId('arrow-up-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('path')).toHaveAttribute('stroke', '#1F4A70');
  });

  test('JobCardArrowDownIcon renders correctly', () => {
    render(<JobCardArrowDownIcon data-testid="arrow-down-icon" />);
    const icon = screen.getByTestId('arrow-down-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('path')).toHaveAttribute('stroke', '#1F4A70');
  });

  test('EditFormDetailIcon renders correctly', () => {
    render(<EditFormDetailIcon data-testid="edit-form-icon" />);
    const icon = screen.getByTestId('edit-form-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('path')).toHaveAttribute('fill', '#1F4A70');
  });

  test('ActionMenuIcon renders correctly', () => {
    render(<ActionMenuIcon data-testid="action-menu-icon" />);
    const icon = screen.getByTestId('action-menu-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelectorAll('path')).toHaveLength(3);
  });

  test('RadioUncheckedIcon renders correctly', () => {
    render(<RadioUncheckedIcon data-testid="radio-unchecked-icon" />);
    const icon = screen.getByTestId('radio-unchecked-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('rect')).toHaveAttribute('fill', 'white');
  });

  test('RadioCheckedIcon renders correctly', () => {
    render(<RadioCheckedIcon data-testid="radio-checked-icon" />);
    const icon = screen.getByTestId('radio-checked-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('rect')).toHaveAttribute('fill', '#0091B8');
  });

  test('Icons accept and pass through props', () => {
    render(
      <SortIcon
        data-testid="props-test-icon"
        className="test-class"
        width="50"
        height="50"
      />,
    );
    const icon = screen.getByTestId('props-test-icon');
    expect(icon).toHaveClass('test-class');
    expect(icon).toHaveAttribute('width', '50');
    expect(icon).toHaveAttribute('height', '50');
  });
});
