import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import CategoriesFiltersDrawer from '../../src/components/CategoriesFiltersDrawer';

jest.mock('../../src/components/SearchInput', () => (props: any) => (
  <input data-testid="mock-search-input" value={props.value} onChange={e => props.onSearch(e.target.value)} />
));
jest.mock('../../src/components/CheckboxComponent', () => (props: any) => (
  <input type="checkbox" data-testid={props.id} checked={props.checked} onChange={props.onChange} />
));
jest.mock('../../src/components/TruncateBasicText', () => (props: any) => <span>{props.text}</span>);
jest.mock('../../src/components/Drawer', () => (props: any) => <div>{props.trigger}<div data-testid="drawer-content">{props.children({ closeDrawer: jest.fn() })}</div></div>);
jest.mock('../../src/components/icons', () => ({ PlusIcon: () => <span data-testid="plus-icon">+</span> }));

const mockContext = {
  dataStore: {
    riskCategoryList: [
      { id: 1, name: 'Cat 1' },
      { id: 2, name: 'Cat 2' },
    ],
    hazardsList: [
      { id: 10, name: 'Haz 1' },
      { id: 20, name: 'Haz 2' },
    ],
  },
};

jest.mock('../../src/context', () => ({
  useDataStoreContext: () => mockContext,
}));

describe('CategoriesFiltersDrawer', () => {
  const filters = { ra_categories: [1], hazard_categories: [10] };
  const onFilterChange = jest.fn();

  it('renders trigger and drawer content', () => {
    render(
      <CategoriesFiltersDrawer filters={filters} onFilterChange={onFilterChange} />
    );
    // Accept either 'Filters' or 'More Filters' in the label
    expect(screen.getByText((content) => /filters/i.test(content))).toBeInTheDocument();
    expect(screen.getByTestId('drawer-content')).toBeInTheDocument();
  });

  it('shows filter counts in label', () => {
    render(
      <CategoriesFiltersDrawer filters={{ ra_categories: [1, 2], hazard_categories: [10] }} onFilterChange={onFilterChange} />
    );
    expect(screen.getByText(/filters/i)).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('calls onFilterChange when clear is clicked', () => {
    render(
      <CategoriesFiltersDrawer filters={filters} onFilterChange={onFilterChange} />
    );
    fireEvent.click(screen.getByText(/clear/i));
    expect(onFilterChange).toHaveBeenCalledWith('ra_categories', null);
    expect(onFilterChange).toHaveBeenCalledWith('hazard_categories', null);
  });

  it('calls onFilterChange when apply is clicked', () => {
    render(
      <CategoriesFiltersDrawer filters={filters} onFilterChange={onFilterChange} />
    );
    fireEvent.click(screen.getByText(/apply/i));
    expect(onFilterChange).toHaveBeenCalledWith('ra_categories', [1]);
    expect(onFilterChange).toHaveBeenCalledWith('hazard_categories', [10]);
  });

  it('renders risk and hazard checkboxes', () => {
    render(
      <CategoriesFiltersDrawer filters={filters} onFilterChange={onFilterChange} />
    );
    expect(screen.getByTestId('category-checked-1')).toBeInTheDocument();
    expect(screen.getByTestId('category-checked-2')).toBeInTheDocument();
    expect(screen.getByTestId('hazard-checked-10')).toBeInTheDocument();
    expect(screen.getByTestId('hazard-checked-20')).toBeInTheDocument();
  });
});
