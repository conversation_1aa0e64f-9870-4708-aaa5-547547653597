import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import SearchDropdown from '../../src/components/SearchDropdown';

jest.mock('../../src/components/SearchInput', () => (props: any) => (
  <input data-testid="mock-search-input" value={props.value} onChange={e => props.onSearch(e.target.value)} />
));
jest.mock('../../src/components/CheckboxComponent', () => (props: any) => (
  <input type="checkbox" data-testid={props.id} checked={props.checked} onChange={props.onChange} />
));
jest.mock('../../src/components/SingleBadgePopover', () => (props: any) => <span data-testid="mock-badge">{props.label}</span>);

const options = [
  { label: 'One', value: 1 },
  { label: 'Two', value: 2 },
  { label: 'Three', value: 3 },
];

describe('SearchDropdown', () => {
  it('renders placeholder when nothing selected', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} placeholder="Pick" />
    );
    expect(screen.getByText('Pick')).toBeInTheDocument();
  });

  it('renders selected values', () => {
    render(
      <SearchDropdown selected={[1, 2]} options={options} onChange={jest.fn()} />
    );
    // Check for both names individually
    expect(screen.getByText('One')).toBeInTheDocument();
    expect(screen.getByText('+1 More')).toBeInTheDocument();
  });

  it('opens dropdown and filters options', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    fireEvent.change(screen.getByTestId('mock-search-input'), { target: { value: 'Two' } });
    expect(screen.getByText('Two')).toBeInTheDocument();
    expect(screen.queryByText('One')).not.toBeInTheDocument();
  });

  it('calls onChange when option is selected', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('One'));
    expect(onChange).toHaveBeenCalledWith([1]);
  });

  it('shows select all/clear all button', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByText('Select all')).toBeInTheDocument();
  });

  it('shows "No options found." when search yields no results', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.change(screen.getByTestId('mock-search-input'), { target: { value: 'nonexistent' } });
    expect(screen.getByText('No options found.')).toBeInTheDocument();
  });

  it('calls onChange for select all', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('Select all'));
    expect(onChange).toHaveBeenCalled();
  });

  it('calls onChange for clear all', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={options.map(o => o.value)} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('Clear all'));
    expect(onChange).toHaveBeenCalled();
  });

  it('handles empty options array', () => {
    render(
      <SearchDropdown selected={[]} options={[]} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByText('No options found.')).toBeInTheDocument();
  });

  it('closes dropdown on outside click', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    fireEvent.mouseDown(document);
    // Dropdown should close, so input should not be in the document
    expect(screen.queryByTestId('mock-search-input')).not.toBeInTheDocument();
  });

  it('keyboard accessibility: Enter, Space, Escape', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    const combobox = screen.getByRole('combobox');
    fireEvent.keyDown(combobox, { key: 'Enter' });
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    fireEvent.keyDown(combobox, { key: 'Escape' });
    expect(screen.queryByTestId('mock-search-input')).not.toBeInTheDocument();
    fireEvent.keyDown(combobox, { key: ' ' });
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
  });

  it('has proper aria attributes', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    const combobox = screen.getByRole('combobox');
    expect(combobox).toHaveAttribute('aria-haspopup', 'listbox');
    expect(combobox).toHaveAttribute('aria-expanded');
    expect(combobox).toHaveAttribute('role', 'combobox');
  });

  it('shows counter and SingleBadgePopover when more than maxDisplayNames selected', () => {
    render(
      <SearchDropdown selected={[1, 2, 3]} options={options} onChange={jest.fn()} maxDisplayNames={1} />
    );
    expect(screen.getByText('+2 More')).toBeInTheDocument();
    expect(screen.getByTestId('mock-badge')).toBeInTheDocument();
  });

  it('does not crash if refs are not set in useLayoutEffect', () => {
    // Simulate missing refs by rendering and not opening dropdown
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    // No error should occur
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('does not call onChange for select all/clear all when no options', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={[]} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    const btn = screen.queryByRole('button', { name: /select all|clear all/i });
    if (btn) {
      fireEvent.click(btn);
    }
    expect(onChange).not.toHaveBeenCalled();
  });

  it('calls onChange when CheckboxComponent onChange is triggered', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    // Click the checkbox directly
    fireEvent.click(screen.getByTestId('option-check-1'));
    expect(onChange).toHaveBeenCalledWith([1]);
  });

  it('does not close dropdown on outside click if dropdownRef is not set', () => {
    // Simulate missing ref by not opening dropdown
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    // No error should occur on outside click
    fireEvent.mouseDown(document);
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('handles setMaxDisplayNames when namesMeasureRef has no children', () => {
    // This is hard to simulate directly, but we can at least ensure no crash
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    // No error should occur
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

});
