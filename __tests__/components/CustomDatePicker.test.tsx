import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import CustomDatePicker from '../../src/components/CustomDatePicker';

// Mock DatePicker
const mockDate = new Date('2024-01-01');
const futureDate = new Date('2025-01-01');

jest.mock('react-datepicker', () => {
  const React = require('react');
  return {
    __esModule: true,
    default: (props: any) => (
      <input
        data-testid="mock-datepicker"
        aria-label={props.placeholderText}
        placeholder={props.placeholderText}
        className={props.className}
        value={props.selected ? props.selected.toDateString() : ''}
        onChange={(e: any) => {
          if (props.onChange) {
            props.onChange(mockDate);
          }
        }}
        onKeyDown={props.onKeyDown}
        readOnly
      />
    ),
  };
});

describe('CustomDatePicker', () => {
  const defaultProps = {
    label: 'Test Date',
    onChange: jest.fn(),
    controlId: 'test-datepicker',
    placeholder: 'Select Date',
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders with label and placeholder', () => {
    render(<CustomDatePicker {...defaultProps} />);
    expect(screen.getByText('Test Date')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Select Date')).toBeInTheDocument();
  });

  test('renders clear icon when value is present and clears on click', () => {
    render(<CustomDatePicker {...defaultProps} value={mockDate} />);
    const clearButton = screen.getByRole('button');
    fireEvent.click(clearButton);
    expect(defaultProps.onChange).toHaveBeenCalledWith(undefined);
  });

  test('shows calendar icon when no value and not required', () => {
    render(<CustomDatePicker {...defaultProps} />);
    expect(screen.getByTestId('mock-datepicker')).toBeInTheDocument();
    expect(
      screen.getByText((_, element) => {
        return element?.tagName === 'svg';
      }),
    ).toBeInTheDocument(); // CalendarIcon renders as SVG
  });

  test('displays default error message when required and errorMsg not provided', () => {
    render(<CustomDatePicker {...defaultProps} isRequired value={undefined} />);
    expect(screen.getByText('Test Date is required.')).toBeInTheDocument();
  });
  test('applies aria-label and correct id', () => {
    render(<CustomDatePicker {...defaultProps} id="custom-id" />);
    expect(screen.getByLabelText('Select Date')).toBeInTheDocument();
    expect(screen.getByText('Test Date').id).toBe('custom-idTest Date');
  });

  test('displays custom error message when provided', () => {
    const customError = 'Please select a valid date';
    render(
      <CustomDatePicker
        {...defaultProps}
        isRequired
        value={undefined}
        errorMsg={customError}
      />,
    );
    expect(screen.getByTestId('error-message')).toHaveTextContent(customError);
  });

  test('prevents keyboard input', () => {
    render(<CustomDatePicker {...defaultProps} />);
    const datePicker = screen.getByTestId('mock-datepicker');
    const keyDownEvent = new KeyboardEvent('keydown', {key: '1'});
    fireEvent.keyDown(datePicker, keyDownEvent);
    expect(defaultProps.onChange).not.toHaveBeenCalled();
  });

  test('renders with min and max date constraints', () => {
    const minDate = new Date('2024-01-01');
    const maxDate = new Date('2024-12-31');
    render(
      <CustomDatePicker
        {...defaultProps}
        minDate={minDate}
        maxDate={maxDate}
      />,
    );
    expect(screen.getByTestId('mock-datepicker')).toBeInTheDocument();
  });

  test('adds invalid class when required and no value', () => {
    render(<CustomDatePicker {...defaultProps} isRequired value={undefined} />);
    const datePicker = screen.getByTestId('mock-datepicker');
    expect(datePicker).toHaveClass('is-invalid');
  });

  test('handles date selection', () => {
    render(<CustomDatePicker {...defaultProps} />);
    const datePicker = screen.getByTestId('mock-datepicker');
    // Create a change event and trigger it
    const changeEvent = {target: {value: mockDate.toDateString()}};
    fireEvent.change(datePicker, changeEvent);
    expect(defaultProps.onChange).toHaveBeenCalledWith(mockDate);
  });

  test('calendar icon is not visible when value is present', () => {
    render(<CustomDatePicker {...defaultProps} value={mockDate} />);
    expect(screen.queryByTestId('calendar-icon')).not.toBeInTheDocument();
  });

  test('clear button is accessible', () => {
    render(<CustomDatePicker {...defaultProps} value={mockDate} />);
    const clearButton = screen.getByTestId('clear-date-button');
    expect(clearButton).toHaveAttribute('aria-label', 'Clear date');
  });
});
