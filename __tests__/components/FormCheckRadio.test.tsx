import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import '@testing-library/jest-dom';
import FormCheckRadio from '../../src/components/FormCheckRadio';

jest.mock('../../src/utils/svgIcons', () => ({
  RadioCheckedIcon: () => <svg data-testid="checked-icon" />,
  RadioUncheckedIcon: () => <svg data-testid="unchecked-icon" />,
}));

describe('FormCheckRadio', () => {
  const defaultProps = {
    name: 'test-radio',
    value: 'option1',
    onChange: jest.fn(),
  };

  it('renders unchecked radio button', () => {
    render(<FormCheckRadio {...defaultProps} checked={false} />);

    expect(screen.getByTestId('unchecked-icon')).toBeInTheDocument();
    expect(screen.queryByTestId('checked-icon')).not.toBeInTheDocument();
  });

  it('renders checked radio button', () => {
    render(<FormCheckRadio {...defaultProps} checked={true} />);

    expect(screen.getByTestId('checked-icon')).toBeInTheDocument();
    expect(screen.queryByTestId('unchecked-icon')).not.toBeInTheDocument();
  });

  it('renders the label when provided', () => {
    render(
      <FormCheckRadio {...defaultProps} checked={true} label="Option 1" />,
    );

    expect(screen.getByText('Option 1')).toBeInTheDocument();
  });
});
