import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import SearchInput from '../../src/components/SearchInput';

describe('SearchInput', () => {
  const mockOnSearch = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with custom placeholder', () => {
    render(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        placeholder="Custom Placeholder"
      />,
    );
    expect(
      screen.getByPlaceholderText('Custom Placeholder'),
    ).toBeInTheDocument();
  });

  it('calls onSearch with null when input is empty or whitespace only', () => {
    render(<SearchInput value="" onSearch={mockOnSearch} />);
    const input = screen.getByRole('textbox');

    fireEvent.change(input, {target: {value: '    '}});
    expect(mockOnSearch).toHaveBeenCalledWith(null);

    fireEvent.change(input, {target: {value: ''}});
    expect(mockOnSearch).toHaveBeenCalledWith(null);
  });

  it('calls onSearch with cleaned value (removes % and _)', () => {
    render(<SearchInput value="" onSearch={mockOnSearch} />);
    const input = screen.getByRole('textbox');

    fireEvent.change(input, {target: {value: 'abc%_def'}});
    expect(mockOnSearch).toHaveBeenCalledWith('abcdef');

    fireEvent.change(input, {target: {value: 'hello_world%'}});
    expect(mockOnSearch).toHaveBeenCalledWith('helloworld');
  });

  it('passes disabled prop to input', () => {
    render(<SearchInput value="" onSearch={mockOnSearch} disabled />);
    expect(screen.getByRole('textbox')).toBeDisabled();
  });

  it('reflects the controlled value prop', () => {
    const {rerender} = render(
      <SearchInput value="initial" onSearch={mockOnSearch} />,
    );
    const input = screen.getByRole('textbox');
    expect(input).toHaveValue('initial');

    rerender(<SearchInput value="updated" onSearch={mockOnSearch} />);
    expect(input).toHaveValue('updated');
  });
});
