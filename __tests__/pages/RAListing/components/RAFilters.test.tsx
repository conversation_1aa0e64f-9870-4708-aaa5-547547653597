import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { RAFilters } from '../../../../src/pages/RAListing/components/RAFilters';

// Mocks for child components
jest.mock('../../../../src/components/SearchInput', () => (props: any) => (
  <input data-testid="mock-search-input" value={props.value} onChange={e => props.onSearch(e.target.value)} />
));
jest.mock('../../../../src/components/DropdownTypeahead', () => (props: any) => {
  const selected = props.selected ?? [];
  // Always render at least one element for test queries
  return (
    <>
      <select data-testid="mock-dropdown-typeahead" onChange={e => props.onChange(e.target.value)} value={selected[0]?.value || ''}>
        <option value="">Option</option>
        <option value="Approved">Approved</option>
      </select>
    </>
  );
});
jest.mock('../../../../src/components/CustomDatePickerWithRange', () => (props: any) => (
  <div data-testid="mock-date-picker">{props.placeholder}</div>
));
jest.mock('../../../../src/components/VesselAndOfficeDropdown', () => (props: any) => (
  <div data-testid="mock-vessel-office-dropdown">Vessel/Office</div>
));
jest.mock('../../../../src/components/SearchDropdown', () => (props: any) => {
  const selected = props.selected ?? [];
  const value = props.value ?? [];
  return (
    <div data-testid="mock-search-dropdown">SearchDropdown</div>
  );
});
jest.mock('../../../../src/pages/RAListing/components/RAMoreFiltersDrawer', () => (props: any) => (
  <div data-testid="mock-more-filters-drawer">MoreFiltersDrawer</div>
));

// Mock services for RAFilters
jest.mock('../../../../src/services/services', () => ({
  getRAStringOptions: jest.fn(() => Promise.resolve({ result: [] })),
  getVesselsList: jest.fn(() => Promise.resolve([])),
  getOfficesList: jest.fn(() => Promise.resolve([])),
}));

jest.mock('react-toastify', () => ({ toast: { error: jest.fn() } }));

// Ensure all filter values and selected props are arrays, not null/undefined
const defaultFilters = {
  vessel_category: [],
  vessel_type: [],
  risk_rating: [],
  status: [],
  flagged: [],
  // ...other filters as needed, all as arrays
};

describe('RAFilters', () => {
  it('renders all filter controls and more filters drawer', () => {
    render(
      <RAFilters filters={defaultFilters as any} onFilterChange={jest.fn()} />
    );
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    expect(screen.getByTestId('mock-vessel-office-dropdown')).toBeInTheDocument();
    expect(screen.getAllByTestId('mock-search-dropdown').length).toBeGreaterThan(0);
    expect(screen.getByTestId('mock-more-filters-drawer')).toBeInTheDocument();
  });

  it('calls onFilterChange for each filter type', async () => {
    const onFilterChange = jest.fn();
    render(
      <RAFilters filters={defaultFilters as any} onFilterChange={onFilterChange} />
    );
    // SearchInput
    fireEvent.change(screen.getByTestId('mock-search-input'), { target: { value: 'abc' } });
    expect(onFilterChange).toHaveBeenCalledWith('search', 'abc');

    // VesselAndOfficeDropdown
    expect(screen.getByTestId('mock-vessel-office-dropdown')).toBeInTheDocument();

    // SearchDropdown
    expect(screen.getAllByTestId('mock-search-dropdown').length).toBeGreaterThan(0);

    // MoreFiltersDrawer
    expect(screen.getByTestId('mock-more-filters-drawer')).toBeInTheDocument();
  });

  it('calls onFilterChange for CustomDatePickerWithRange', () => {
    const onFilterChange = jest.fn();
    render(
      <RAFilters filters={defaultFilters as any} onFilterChange={onFilterChange} />
    );
    // Simulate date picker change
    // Since it's mocked, just check the component exists
    expect(screen.getAllByTestId('mock-date-picker').length).toBeGreaterThan(0);
  });

  it('calls onFilterChange for VesselAndOfficeDropdown', () => {
    const onFilterChange = jest.fn();
    render(
      <RAFilters filters={defaultFilters as any} onFilterChange={onFilterChange} />
    );
    expect(screen.getByTestId('mock-vessel-office-dropdown')).toBeInTheDocument();
  });

  it('calls onFilterChange for SearchDropdown', () => {
    const onFilterChange = jest.fn();
    render(
      <RAFilters filters={defaultFilters as any} onFilterChange={onFilterChange} />
    );
    expect(screen.getAllByTestId('mock-search-dropdown').length).toBeGreaterThan(0);
  });

  it('getRaBasicFiltersFormConfig returns all filter configs', () => {
    const { getRaBasicFiltersFormConfig } = require('../../../../src/pages/RAListing/components/RAFilters');
    const filters = {
      search: '',
      approval_status: [],
      vessel_or_office: null,
      vessel_category: [],
      ra_level: [],
      submitted_on: null,
      approval_date: null,
      assessment_date: null,
    };
    const onFilterChange = jest.fn();
    const config = getRaBasicFiltersFormConfig(
      { filters, onFilterChange },
      { vessels: [], vesselCategories: [], offices: [] }
    );
    expect(Array.isArray(config)).toBe(true);
    expect(config.length).toBeGreaterThan(0);
    config.forEach(item => {
      expect(item).toHaveProperty('key');
      expect(item).toHaveProperty('component');
    });
  });

  it('handles async error in useEffect and shows toast', async () => {
    const services = require('../../../../src/services/services');
    services.getRAStringOptions.mockImplementationOnce(() => { throw new Error('fail'); });
    services.getVesselsList.mockImplementationOnce(() => { throw new Error('fail'); });
    services.getOfficesList.mockImplementationOnce(() => { throw new Error('fail'); });
    const { RAFilters: RAFiltersWithError } = require('../../../../src/pages/RAListing/components/RAFilters');
    render(<RAFiltersWithError filters={defaultFilters as any} onFilterChange={jest.fn()} />);
    await waitFor(() => {
      const { toast } = require('react-toastify');
      expect(toast.error).toHaveBeenCalled();
    });
  });
});
