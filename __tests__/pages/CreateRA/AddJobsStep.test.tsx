import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import {AddJobsStep} from '../../../src/pages/CreateRA/AddJobsStep';
import {TemplateForm} from '../../../src/types/template';
import {TemplateFormStatus} from '../../../src/enums';

// Mock dependencies
jest.mock('../../../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

jest.mock('../../../src/components/InputComponent', () => ({
  InputComponent: ({
    label,
    name,
    value,
    onChange,
    placeholder,
    type,
    form,
  }: any) => (
    <div data-testid={`input-${name}`}>
      <label htmlFor={name}>{label}</label>
      <input
        id={name}
        name={name}
        value={form?.[name] || value || ''}
        onChange={onChange}
        placeholder={placeholder}
        type={type === 'textarea' ? 'text' : type}
        data-testid={`input-field-${name}`}
      />
    </div>
  ),
}));

jest.mock('../../../src/components/InitialRiskRatingModal', () => {
  const mockModal = ({show, onHide, onSelect, selectedValue}: any) =>
    show ? (
      <div data-testid="initial-risk-rating-modal">
        <button data-testid="modal-close" onClick={onHide}>
          Close
        </button>
        <button data-testid="modal-select-A1" onClick={() => onSelect('A1')}>
          A1
        </button>
        <button data-testid="modal-select-E5" onClick={() => onSelect('E5')}>
          E5
        </button>
        <div data-testid="selected-value">{selectedValue}</div>
      </div>
    ) : null;

  return {
    __esModule: true,
    default: mockModal,
    consequenceRows: [
      {label: 'Catastrophic (E)'},
      {label: 'Major (D)'},
      {label: 'Moderate (C)'},
      {label: 'Minor (B)'},
      {label: 'Insignificant (A)'},
    ],
    likelihoodCols: [
      {label: 'Rare (1)'},
      {label: 'Unlikely (2)'},
      {label: 'Possible (3)'},
      {label: 'Likely (4)'},
      {label: 'Almost Certain (5)'},
    ],
    getCellColor: jest.fn((code: string) => {
      if (['A1', 'A2', 'A3', 'B1', 'B2', 'C1'].includes(code)) return '#28A747';
      if (['C5', 'D4', 'D5', 'E3', 'E4', 'E5'].includes(code)) return '#D41B56';
      return '#FFC107';
    }),
  };
});

jest.mock('../../../src/components/CustomDatePicker', () => ({
  __esModule: true,
  default: ({label, value, onChange, placeholder, controlId}: any) => (
    <div data-testid={`date-picker-${controlId}`}>
      <label>{label}</label>
      <input
        type="date"
        value={value ? value.toISOString().slice(0, 10) : ''}
        onChange={e =>
          onChange(e.target.value ? new Date(e.target.value) : undefined)
        }
        placeholder={placeholder}
        data-testid={`date-input-${controlId}`}
      />
    </div>
  ),
}));

jest.mock('../../../src/utils/svgIcons', () => ({
  DeleteJobIcon: () => <span data-testid="delete-job-icon">Delete</span>,
  JobCardArrowDownIcon: () => <span data-testid="arrow-down-icon">▼</span>,
  JobCardArrowUpIcon: () => <span data-testid="arrow-up-icon">▲</span>,
}));

// Mock window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: jest.fn(),
});

describe('AddJobsStep Component', () => {
  const mockSetForm = jest.fn();
  const mockOnValidate = jest.fn();

  const mockRiskParameters = [
    {id: 1, name: 'People'},
    {id: 2, name: 'Environment'},
    {id: 3, name: 'Asset'},
  ];

  const defaultForm: TemplateForm = {
    task_requiring_ra: 'Test Task',
    task_duration: '5',
    task_alternative_consideration: 'Alternative',
    task_rejection_reason: 'Reason',
    worst_case_scenario: 'Scenario',
    recovery_measures: 'Measures',
    status: TemplateFormStatus.DRAFT,
    parameters: [],
    template_category: {
      category_id: [],
    },
    template_hazard: {
      is_other: false,
      value: '',
      hazard_id: [],
    },
    template_job: [
      {
        job_id: 'test-job-id',
        job_step: 'Test Job Step',
        job_hazard: 'Test Hazard',
        job_nature_of_risk: 'Test Risk',
        job_existing_control: 'Test Control',
        job_additional_mitigation: 'Test Mitigation',
        job_close_out_date: '2024-12-31',
        job_close_out_responsibility_id: '1',
        template_job_initial_risk_rating: [
          {parameter_type_id: 1, rating: 'A1'},
        ],
        template_job_residual_risk_rating: [
          {parameter_type_id: 1, rating: 'A1', reason: 'Test reason'},
        ],
      },
    ],
    template_task_reliability_assessment: [],
    template_keyword: [],
  };

  const mockUseDataStoreContext = {
    dataStore: {
      riskParameterListForRiskRaiting: mockRiskParameters,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (
      require('../../../src/context').useDataStoreContext as jest.Mock
    ).mockReturnValue(mockUseDataStoreContext);
  });

  const renderAddJobsStep = (
    form = defaultForm,
    setForm = mockSetForm,
    onValidate = mockOnValidate,
  ) => {
    const ref = React.createRef<any>();
    render(<AddJobsStep ref={ref} form={form} setForm={setForm} />);
    return {ref};
  };

  describe('Component Rendering', () => {
    it('renders the component with task title', () => {
      renderAddJobsStep();

      expect(screen.getByText('Test Task')).toBeInTheDocument();
    });

    it('renders guidance and risk matrix buttons', () => {
      renderAddJobsStep();

      expect(screen.getByText('Guidance Table')).toBeInTheDocument();
      expect(screen.getByText('Risk Matrix Table')).toBeInTheDocument();
    });

    it('renders add job button', () => {
      renderAddJobsStep();

      expect(screen.getByText('+ Add Job')).toBeInTheDocument();
    });

    it('renders job cards when jobs exist', () => {
      renderAddJobsStep();

      expect(screen.getByText('Job 1')).toBeInTheDocument();
    });

    it('renders expand job cards link when multiple jobs exist', () => {
      const formWithMultipleJobs = {
        ...defaultForm,
        template_job: [
          defaultForm.template_job[0],
          {
            ...defaultForm.template_job[0],
            job_id: 'test-job-id-2',
            job_step: 'Second Job',
          },
        ],
      };

      renderAddJobsStep(formWithMultipleJobs);

      expect(screen.getByText('Expand Job Cards')).toBeInTheDocument();
    });

    it('displays default task title when task_requiring_ra is empty', () => {
      const formWithoutTask = {...defaultForm, task_requiring_ra: ''};
      renderAddJobsStep(formWithoutTask);

      expect(screen.getByText('ssss')).toBeInTheDocument();
    });

    it('renders hazard and control measures section', () => {
      renderAddJobsStep();

      expect(screen.getByText('Hazard & Control Measures')).toBeInTheDocument();
    });
  });

  describe('Job Management', () => {
    it('expands job card when header is clicked', () => {
      renderAddJobsStep();

      const jobHeader = screen.getByText('Job 1');
      fireEvent.click(jobHeader);

      // Should show form fields when expanded
      expect(screen.getByText('Job Step')).toBeInTheDocument();
      expect(screen.getByText('Hazard')).toBeInTheDocument();
      expect(screen.getByText('Nature of Risk')).toBeInTheDocument();
    });

    it('shows job step in collapsed header when available', () => {
      renderAddJobsStep();

      // Job should show step name when collapsed (initially the first job is expanded)
      // Let's click to collapse it first
      const jobHeader = screen.getByText('Job 1');
      fireEvent.click(jobHeader);

      // Now it should show the job step in collapsed state
      expect(screen.getByText('Test Job Step')).toBeInTheDocument();
    });

    it('toggles job card expansion correctly', () => {
      renderAddJobsStep();

      const jobHeader = screen.getByText('Job 1');

      // Initially expanded (first job is expanded by default) - should show up arrow
      expect(screen.getByTestId('arrow-up-icon')).toBeInTheDocument();

      // Click to collapse
      fireEvent.click(jobHeader);

      // Should show down arrow when collapsed
      expect(screen.getByTestId('arrow-down-icon')).toBeInTheDocument();

      // Click again to expand
      fireEvent.click(jobHeader);

      // Should show up arrow again
      expect(screen.getByTestId('arrow-up-icon')).toBeInTheDocument();
    });

    it('deletes job when delete button is clicked', () => {
      renderAddJobsStep();

      // The job is already expanded by default, so delete button should be visible
      const deleteButton = screen.getByTestId('delete-job-icon');
      fireEvent.click(deleteButton.closest('button')!);

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

      // Test the function passed to setForm
      const setFormCall =
        mockSetForm.mock.calls[mockSetForm.mock.calls.length - 1][0];
      const newForm = setFormCall(defaultForm);
      expect(newForm.template_job).toHaveLength(0);
    });

    it('expands all job cards when expand job cards link is clicked', () => {
      const formWithMultipleJobs = {
        ...defaultForm,
        template_job: [
          defaultForm.template_job[0],
          {
            ...defaultForm.template_job[0],
            job_id: 'test-job-id-2',
            job_step: 'Second Job',
          },
        ],
      };

      renderAddJobsStep(formWithMultipleJobs);

      const expandAllLink = screen.getByText('Expand Job Cards');
      fireEvent.click(expandAllLink);

      // Both jobs should be expanded and show form fields
      expect(screen.getAllByText('Job Step')).toHaveLength(2);
    });
  });

  describe('Form Field Updates', () => {
    beforeEach(() => {
      // Expand job card for form field tests
      renderAddJobsStep();
      const jobHeader = screen.getByText('Job 1');
      fireEvent.click(jobHeader);
    });

    it('updates job step field', () => {
      const jobStepInput = screen.getByTestId('input-field-job_step');
      fireEvent.change(jobStepInput, {target: {value: 'Updated Job Step'}});

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    });

    it('updates job hazard field', () => {
      const hazardInput = screen.getByTestId('input-field-job_hazard');
      fireEvent.change(hazardInput, {target: {value: 'Updated Hazard'}});

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    });

    it('updates nature of risk field', () => {
      const riskInput = screen.getByTestId('input-field-job_nature_of_risk');
      fireEvent.change(riskInput, {target: {value: 'Updated Risk'}});

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    });

    it('updates existing control field', () => {
      const controlInput = screen.getByTestId(
        'input-field-job_existing_control',
      );
      fireEvent.change(controlInput, {target: {value: 'Updated Control'}});

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    });

    it('updates additional mitigation field', () => {
      const mitigationInput = screen.getByTestId(
        'input-field-job_additional_mitigation',
      );
      fireEvent.change(mitigationInput, {
        target: {value: 'Updated Mitigation'},
      });

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    });

    it('displays form field values correctly', () => {
      expect(screen.getByDisplayValue('Test Job Step')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Hazard')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Risk')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Control')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Mitigation')).toBeInTheDocument();
    });
  });

  describe('Risk Rating Functionality', () => {
    beforeEach(() => {
      renderAddJobsStep();
      const jobHeader = screen.getByText('Job 1');
      fireEvent.click(jobHeader);
    });

    it('displays initial risk rating section', () => {
      expect(screen.getByText('Initial Risk Rating')).toBeInTheDocument();
      expect(
        screen.getByText('Click the cards to set the Risk Ratings'),
      ).toBeInTheDocument();
    });

    it('displays residual risk rating section', () => {
      expect(screen.getByText('Residual Risk Rating')).toBeInTheDocument();
      expect(screen.getByText('Reason for Lowering')).toBeInTheDocument();
    });

    it('shows risk parameters for each rating type', () => {
      // Should show People parameter for both initial and residual
      expect(screen.getAllByText('People:')).toHaveLength(2);
    });

    it('displays existing risk ratings', () => {
      // Should show the existing A1 rating
      expect(screen.getAllByText(/A1.*Insignificant.*Rare/)).toHaveLength(2);
    });

    it('opens risk rating modal when initial risk rating is clicked', () => {
      const riskRatingButtons = screen.getAllByText(/A1.*Insignificant.*Rare/);
      fireEvent.click(riskRatingButtons[0]); // Click initial risk rating

      expect(
        screen.getByTestId('initial-risk-rating-modal'),
      ).toBeInTheDocument();
    });

    it('opens risk rating modal when residual risk rating is clicked', () => {
      const riskRatingButtons = screen.getAllByText(/A1.*Insignificant.*Rare/);
      fireEvent.click(riskRatingButtons[1]); // Click residual risk rating

      expect(
        screen.getByTestId('initial-risk-rating-modal'),
      ).toBeInTheDocument();
    });

    it('handles risk rating selection from modal', () => {
      const riskRatingButtons = screen.getAllByText(/A1.*Insignificant.*Rare/);
      fireEvent.click(riskRatingButtons[0]); // Click initial risk rating

      const selectButton = screen.getByTestId('modal-select-E5');
      fireEvent.click(selectButton);

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    });

    it('closes modal when close button is clicked', () => {
      const riskRatingButtons = screen.getAllByText(/A1.*Insignificant.*Rare/);
      fireEvent.click(riskRatingButtons[0]);

      const closeButton = screen.getByTestId('modal-close');
      fireEvent.click(closeButton);

      expect(
        screen.queryByTestId('initial-risk-rating-modal'),
      ).not.toBeInTheDocument();
    });

    it('updates reason for lowering when text is entered', () => {
      // Get the first reason input (for People parameter)
      const reasonInputs = screen.getAllByPlaceholderText('Enter the Reason');
      fireEvent.change(reasonInputs[0], {target: {value: 'Updated reason'}});

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    });

    it('displays existing reason for lowering', () => {
      expect(screen.getByDisplayValue('Test reason')).toBeInTheDocument();
    });
  });
// Close Out Section tests removed as functionality is not currently active


  describe('External Links', () => {
    it('opens guidance table PDF when button is clicked', () => {
      renderAddJobsStep();

      const guidanceButton = screen.getByText('Guidance Table');
      fireEvent.click(guidanceButton);

      expect(window.open).toHaveBeenCalledWith('test-file-stub', '_blank');
    });

    it('opens risk matrix PDF when button is clicked', () => {
      renderAddJobsStep();

      const riskMatrixButton = screen.getByText('Risk Matrix Table');
      fireEvent.click(riskMatrixButton);

      expect(window.open).toHaveBeenCalledWith('test-file-stub', '_blank');
    });
  });

  describe('Empty State', () => {
    it('renders correctly when no jobs exist', () => {
      const emptyForm = {...defaultForm, template_job: []};
      renderAddJobsStep(emptyForm);

      expect(screen.getByText('+ Add Job')).toBeInTheDocument();
      expect(screen.queryByText('Job 1')).not.toBeInTheDocument();
      expect(screen.queryByText('Expand Job Cards')).not.toBeInTheDocument();
    });

    // it('adds first job correctly when no jobs exist', () => {
    //   const emptyForm = {...defaultForm, template_job: []};
    //   renderAddJobsStep(emptyForm);

    //   const addButton = screen.getByText('+ Add Job');
    //   fireEvent.click(addButton);

    //   expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    // });
  });

  describe('Keyboard Navigation', () => {
    beforeEach(() => {
      renderAddJobsStep();
      const jobHeader = screen.getByText('Job 1');
      fireEvent.click(jobHeader);
    });

    it('handles keyboard navigation for expand job cards link', () => {
      const formWithMultipleJobs = {
        ...defaultForm,
        template_job: [
          defaultForm.template_job[0],
          {
            ...defaultForm.template_job[0],
            job_id: 'test-job-id-2',
            job_step: 'Second Job',
          },
        ],
      };

      renderAddJobsStep(formWithMultipleJobs);

      const expandAllLink = screen.getByText('Expand Job Cards');
      fireEvent.keyDown(expandAllLink, {key: 'Enter'});

      // Should not cause any errors
      expect(expandAllLink).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles missing risk parameters gracefully', () => {
      const mockContextWithoutParams = {
        dataStore: {
          riskParameterListForRiskRaiting: [],
        },
      };

      (
        require('../../../src/context').useDataStoreContext as jest.Mock
      ).mockReturnValue(mockContextWithoutParams);

      renderAddJobsStep();
      const jobHeader = screen.getByText('Job 1');
      fireEvent.click(jobHeader);

      expect(screen.getByText('Initial Risk Rating')).toBeInTheDocument();
      expect(screen.getByText('Residual Risk Rating')).toBeInTheDocument();
    });

    it('handles job without initial risk rating for residual section', () => {
      const formWithoutInitialRating = {
        ...defaultForm,
        template_job: [
          {
            ...defaultForm.template_job[0],
            template_job_initial_risk_rating: [],
          },
        ],
      };

      renderAddJobsStep(formWithoutInitialRating);
      const jobHeader = screen.getByText('Job 1');
      fireEvent.click(jobHeader);

      // Residual risk rating should be disabled when no initial rating
      expect(screen.getByText('Residual Risk Rating')).toBeInTheDocument();
    });

    // it('handles form updates correctly', () => {
    //   renderAddJobsStep();

    //   // Test that form updates work with the callback pattern
    //   const addButton = screen.getByText('+ Add Job');
    //   fireEvent.click(addButton);

    //   const setFormCallback = mockSetForm.mock.calls[0][0];
    //   const result = setFormCallback(defaultForm);

    //   expect(result.template_job).toHaveLength(2);
    //   expect(result.template_job[1].job_step).toBe('');
    // });
  });

  describe('Validation', () => {
    it('validates required fields correctly', () => {
      const {ref} = renderAddJobsStep();

      // Call validate method through ref
      const isValid = ref.current?.validate();

      expect(isValid).toBe(true); // Should be valid with default form data
    });

    it('validates empty form correctly', () => {
      const emptyJobForm = {
        ...defaultForm,
        template_job: [
          {
            job_id: 'empty-job',
            job_step: '',
            job_hazard: '',
            job_nature_of_risk: '',
            job_existing_control: '',
            job_additional_mitigation: '',
            job_close_out_date: '',
            job_close_out_responsibility_id: '',
            template_job_initial_risk_rating: [],
            template_job_residual_risk_rating: [],
          },
        ],
      };

      const {ref} = renderAddJobsStep(emptyJobForm);

      const isValid = ref.current?.validate();

      expect(isValid).toBe(false); // Should be invalid with empty required fields
    });

    it('validates form with no jobs', () => {
      const noJobsForm = {
        ...defaultForm,
        template_job: [],
      };

      const {ref} = renderAddJobsStep(noJobsForm);

      const isValid = ref.current?.validate();

      expect(isValid).toBe(false); // Should be invalid with no jobs
    });
  });

  describe('Risk Rating Utilities', () => {
    it('handles risk rating modal with correct title', () => {
      renderAddJobsStep();
      const jobHeader = screen.getByText('Job 1');
      fireEvent.click(jobHeader);

      const riskRatingButtons = screen.getAllByText(/A1.*Insignificant.*Rare/);
      fireEvent.click(riskRatingButtons[0]); // Click initial risk rating

      // Check that modal is opened
      expect(
        screen.getByTestId('initial-risk-rating-modal'),
      ).toBeInTheDocument();
    });

    it('handles residual risk rating modal with correct title', () => {
      renderAddJobsStep();
      const jobHeader = screen.getByText('Job 1');
      fireEvent.click(jobHeader);

      const riskRatingButtons = screen.getAllByText(/A1.*Insignificant.*Rare/);
      fireEvent.click(riskRatingButtons[1]); // Click residual risk rating

      // Check that modal is opened
      expect(
        screen.getByTestId('initial-risk-rating-modal'),
      ).toBeInTheDocument();
    });

    it('displays correct IRR value in residual modal', () => {
      renderAddJobsStep();
      const jobHeader = screen.getByText('Job 1');
      fireEvent.click(jobHeader);

      const riskRatingButtons = screen.getAllByText(/A1.*Insignificant.*Rare/);
      fireEvent.click(riskRatingButtons[1]); // Click residual risk rating

      // Should show the initial risk rating value in the modal
      expect(
        screen.getByTestId('initial-risk-rating-modal'),
      ).toBeInTheDocument();
    });
  });

  describe('Form Field Interactions', () => {
    beforeEach(() => {
      renderAddJobsStep();
      const jobHeader = screen.getByText('Job 1');
      fireEvent.click(jobHeader);
    });

    it('handles onBlur events for form fields', () => {
      const jobStepInput = screen.getByTestId('input-field-job_step');
      fireEvent.blur(jobStepInput);

      // Should not cause any errors
      expect(jobStepInput).toBeInTheDocument();
    });

    it('handles maxLength constraints', () => {
      const jobStepInput = screen.getByTestId('input-field-job_step');

      // Try to enter text longer than maxLength (255)
      const longText = 'a'.repeat(300);
      fireEvent.change(jobStepInput, {target: {value: longText}});

      expect(mockSetForm).toHaveBeenCalled();
    });
  });

  describe('Component Props', () => {
    it('works without onValidate callback', () => {
      const {ref} = renderAddJobsStep(defaultForm, mockSetForm, undefined);

      // Should not throw error when onValidate is undefined
      expect(() => ref.current?.validate()).not.toThrow();
    });
  });
});
