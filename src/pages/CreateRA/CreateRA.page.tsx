import React, {useEffect, useRef, useState} from 'react';
import * as _ from 'lodash';
import {toast} from 'react-toastify';
import {useDataStoreContext} from '../../context';
import GenericStepper, {StepConfig} from '../../components/GenericStepper';
import {BasicDetails} from './BasicDetails';
import {AddJobsStep} from './AddJobsStep';
import {AtRiskStep} from './AtRiskStep';
import {HazardCategoryStep} from './HazardCategoryStep';
import {RaCategoryStep} from './RaCategoryStep';
import {RiskRatingStep} from './RiskRatingStep';
import {
  createNewRA,
  createNewTemplate,
  getApprovalsRequiredList,
  getCrewList,
  getHazardsList,
  getMainRiskParameterType,
  getOfficesList,
  getRiskById,
  getRiskCategoryList,
  getRiskParameterType,
  getTaskReliabilityAssessList,
  getTemplateById,
  getVesselsList,
  updateSavedRA,
  updateSavedTemplate,
} from '../../services/services';
import {useLocation, useNavigate} from 'react-router-dom';
import {TemplateForm} from '../../types/template';
import {RiskForm} from '../../types/risk';
import PreviewFormDetails from './PreviewFormDetails';
import {ConfirmPublishDetailsModal} from '../../components/ConfirmPublishDetailsModal';
import {TemplateFormStatus} from '../../enums';
import {
  createFormFromData,
  createRiskFormFromData,
  formParameterHandler,
  transformTemplateToRisk,
} from '../../utils/helper';
import {AddTeamMembersStep} from './AddTeamMembersStep';
import Loader from '../../components/Loader';

export const StepperPage = () => {
  const {pathname} = useLocation();
  let type: 'template' | 'risk' | undefined;
  let templateFormID: string | null = null;
  let defaultTemplateId: string | null = null;

  // New URL matching logic for all supported paths
  // 1. /risk-assessment/templates/create
  // 2. /risk-assessment/templates/:id
  // 3. /risk-assessment/risks/create
  // 4. /risk-assessment/risks/:id
  // 5. /risk-assessment/templates/:id/risks/create (id here is defaultTemplateId, not templateFormID)

  // Match for create RA using template (special case)
  const templateWithIdRegex = /^\/risk-assessment\/templates\/([^/]+)$/;
  const riskWithIdRegex = /^\/risk-assessment\/risks\/([^/]+)$/;
  const matchCreateRAUsingTemplate =
    /^\/risk-assessment\/templates\/([^/]+)\/risks\/create$/.exec(pathname);
  if (matchCreateRAUsingTemplate) {
    defaultTemplateId = matchCreateRAUsingTemplate[1];
    type = 'risk';
  } else if (/^\/risk-assessment\/templates\/create$/.test(pathname)) {
    type = 'template';
  } else if (templateWithIdRegex.test(pathname)) {
    const match = templateWithIdRegex.exec(pathname);
    templateFormID = match ? match[1] : null;
    type = 'template';
  } else if (/^\/risk-assessment\/risks\/create$/.test(pathname)) {
    type = 'risk';
  } else if (riskWithIdRegex.test(pathname)) {
    const match = riskWithIdRegex.exec(pathname);
    templateFormID = match ? match[1] : null;
    type = 'risk';
  }

  const {setDataStore} = useDataStoreContext();
  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const navigate = useNavigate();
  const [openPreview, setOpenPreview] = useState(false);
  const [stepValid, setStepValid] = useState(false);
  const [loadStep, setLoadStep] = useState(1);
  const [showConfirmPublishDetailsModal, setShowConfirmPublishDetailsModal] =
    useState(false);
  const [keywords, setKeywords] = useState<string[]>([]);
  const [form, setForm] = useState<TemplateForm | RiskForm>(
    type === 'risk' ? createRiskFormFromData() : createFormFromData(),
  );

  useEffect(() => {
    const fetchInitialFormData = async () => {
      if (defaultTemplateId?.length) {
        setLoading(true);
        try {
          const defualtTemplate = await getTemplateById(defaultTemplateId);
          if (defualtTemplate.result) {
            const data = transformTemplateToRisk(defualtTemplate.result);
            setForm(data);
          }
        } catch (err) {
          console.error('Error fetching default template:', err);
        } finally {
          setLoading(false);
        }
      }
    };
    fetchInitialFormData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, defaultTemplateId]);
  const basicDetailsRef = useRef<any>(null);
  const addTeamMembersRef = useRef<any>(null);
  const raCategoryRef = useRef<any>(null);
  const hazardCategoryRef = useRef<any>(null);
  const atRiskRef = useRef<any>(null);
  const addJobs = useRef<any>(null);

  const baseSteps: StepConfig[] = [
    {
      label: type === 'risk' ? 'Confirm Basic Details' : 'Basic Details',
      component: (
        <BasicDetails
          ref={basicDetailsRef}
          form={form}
          setForm={setForm}
          onValidate={setStepValid}
          type={type}
        />
      ),
    },
    {
      label: type === 'risk' ? 'Confirm RA Category' : 'Identify RA Category',
      component: (
        <RaCategoryStep
          ref={raCategoryRef}
          form={form}
          setForm={setForm}
          onValidate={setStepValid}
          type={type}
        />
      ),
    },
    {
      label:
        type === 'risk'
          ? 'Confirm Hazard Category'
          : 'Identify Hazard Category',
      component: (
        <HazardCategoryStep
          ref={hazardCategoryRef}
          form={form}
          setForm={setForm}
          onValidate={setStepValid}
          type={type}
        />
      ),
    },
    {
      label: 'Who & What is At Risk',
      component: (
        <AtRiskStep
          ref={atRiskRef}
          form={form}
          setForm={setForm}
          onValidate={setStepValid}
        />
      ),
    },
    {
      label:
        type === 'risk' ? 'Confirm Associated Jobs' : 'Add Associated Jobs',
      component: (
        <AddJobsStep
          form={form}
          setForm={setForm}
          ref={addJobs}
          onValidate={setStepValid}
          type={type}
        />
      ),
    },
    {
      label: 'Overall Risk Rating',
      component: (
        <RiskRatingStep
          ref={atRiskRef}
          form={form}
          setForm={setForm}
          onValidate={setStepValid}
        />
      ),
    },
  ];

  // Insert MockStep at position 2 (index 1) when type is 'risk'
  const steps: StepConfig[] =
    type === 'risk'
      ? [
          baseSteps[0], // Basic Details
          {
            label: 'Add Team Members',
            component: (
              <AddTeamMembersStep
                ref={addTeamMembersRef}
                form={form}
                setForm={setForm}
                onValidate={setStepValid}
              />
            ),
          },
          ...baseSteps.slice(1), // All remaining steps shifted by 1
        ]
      : baseSteps;
  const fetchTemplateData = async (templateId: string) => {
    setLoading(true);
    try {
      const response =
        type === 'risk'
          ? await getRiskById(templateId)
          : await getTemplateById(templateId);
      const data = response.result;

      const formData =
        type === 'risk'
          ? createRiskFormFromData(data)
          : createFormFromData(data);

      if (type === 'risk') {
        const vesselId = (formData as RiskForm).vessel_id;
        if (vesselId) {
          const resp = await getCrewList(vesselId);
          setDataStore(prev => ({
            ...prev,
            crewMembersListForRisk: resp,
          }));
        }
      }
      setForm(formData);
      if (defaultTemplateId) {
        setLoadStep(1);
        validateStep(1);
      } else if (data?.draft_step && data?.draft_step < steps.length + 1) {
        setLoadStep(data.draft_step ?? 1);
        validateStep(data.draft_step ?? 1);
      } else setOpenPreview(true);
    } catch (err) {
      console.error('Error fetching draft', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!templateFormID || !type) return;
    fetchTemplateData(templateFormID);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [templateFormID, type]);

  useEffect(() => {
    const loadBasicDetails = async () => {
      try {
        setLoading(true);
        const [
          categorListData,
          hazardsListData,
          riskParameterData,
          taskRelAssessData,
          mainRiskParameterData,
          mainRiskParameterDataForRiskRating,
          vesselResponse,
          officeResponse,
          approvalList,
        ] = await Promise.all([
          getRiskCategoryList(),
          getHazardsList(),
          getRiskParameterType(),
          getTaskReliabilityAssessList(),
          getMainRiskParameterType(),
          getMainRiskParameterType(true),
          getVesselsList(),
          getOfficesList(),
          getApprovalsRequiredList(),
        ]);

        const groupedRiskParameterData = _.chain(riskParameterData)
          .groupBy(item => item?.parameter_type?.id)
          .map(items => ({
            id: items[0]?.parameter_type?.id,
            name: items[0]?.parameter_type?.name,
            parameters: items?.map(i => ({
              id: i?.id,
              name: i?.name,
            })),
          }))
          .value();

        setDataStore(prev => ({
          ...prev,
          riskCategoryList: categorListData,
          hazardsList: hazardsListData,
          riskParameterType: groupedRiskParameterData,
          taskReliabilityAssessList: taskRelAssessData,
          riskParameterList: mainRiskParameterData,
          riskParameterListForRiskRaiting: mainRiskParameterDataForRiskRating,
          vesselListForRisk: vesselResponse,
          officeListForRisk: officeResponse,
          approversReqListForRisk: approvalList,
        }));
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    loadBasicDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePreview = () => {
    // Handle preview logic her
    setOpenPreview(true);
  };

  const validateStep = (step: number): boolean => {
    const refs =
      type === 'risk'
        ? [
            basicDetailsRef,
            addTeamMembersRef,
            raCategoryRef,
            hazardCategoryRef,
            atRiskRef,
            addJobs,
          ]
        : [
            basicDetailsRef,
            raCategoryRef,
            hazardCategoryRef,
            atRiskRef,
            addJobs,
          ];

    if (step >= 1 && step <= refs.length) {
      const ref = refs[step - 1];
      if (ref.current && !ref.current.validate()) {
        setStepValid(false);
        return false;
      }
    }
    setStepValid(true);
    return true;
  };

  const handleNext = async (currentStep: number): Promise<void> => {
    if (currentStep === 2 && type === 'risk') {
      const vesselId = (form as RiskForm).vessel_id;
      if (vesselId) {
        const resp = await getCrewList(vesselId);
        setDataStore(prev => ({
          ...prev,
          crewMembersListForRisk: resp,
        }));
      }
    }
    window.scrollTo({top: 0, behavior: 'smooth'});
    validateStep(currentStep);
  };

  const handleSaveToDraft = async (currentStep: number): Promise<void> => {
    validateStep(currentStep);
    const payload = {
      ...form,
      draft_step: currentStep,
    };
    await formParameterHandler(payload);
    if ('vessel_id' in payload) {
      delete (payload as any).vessel_id;
    }
    if (defaultTemplateId && type === 'risk')
      (payload as any).template_id = defaultTemplateId;
    try {
      const alertMessage = type === 'risk' ? 'RA' : 'Template';
      setLoader(true);
      if (templateFormID) {
        type === 'risk'
          ? await updateSavedRA(templateFormID, payload)
          : await updateSavedTemplate(templateFormID, payload);
        setLoader(false);
        toast.success(`${alertMessage} draft updated successfully`);
        if (currentStep > steps.length)
          navigate(
            `/risk-assessment${type === 'risk' ? '' : '/template-listing'}`,
          );
      } else {
        const result =
          type === 'risk'
            ? await createNewRA(payload)
            : await createNewTemplate(payload);
        setLoader(false);
        toast.success(`${alertMessage} draft saved successfully`);
        if (currentStep > steps.length)
          navigate(
            `/risk-assessment${type === 'risk' ? '' : '/template-listing'}`,
          );
        if (result.id) {
          // Storing the form id in session storage
          // Update URL to reflect the new template or risk ID as per new URL patterns
          if (type === 'template') {
            window.history.replaceState(
              {},
              '',
              `/risk-assessment/templates/${result.id}${window.location.search}`,
            );
          } else if (type === 'risk') {
            window.history.replaceState(
              {},
              '',
              `/risk-assessment/risks/${result.id}${window.location.search}`,
            );
          }
          setForm(formData => ({
            ...formData,
            id: result.id,
          }));
        }
      }
    } catch (err) {
      setLoader(false);
      toast.error('Error saving draft. Changes were not saved');
      console.error('Error saving draft:', err);
    }
  };
  const handelFormPublish = async (updatedData: string[]) => {
    setKeywords(updatedData);
    const payload = {
      ...form,
      status: TemplateFormStatus.PUBLISHED,
    };
    await formParameterHandler(payload);
    if ('vessel_id' in payload) {
      delete (payload as any).vessel_id;
    }
    if (type !== 'risk') (payload as any).template_keyword = updatedData;
    if (defaultTemplateId && type === 'risk')
      (payload as any).template_id = defaultTemplateId;
    const alertMessage = type === 'risk' ? 'RA' : 'Template';
    try {
      setLoading(true);
      if (templateFormID) {
        type === 'risk'
          ? await updateSavedRA(templateFormID, payload)
          : await updateSavedTemplate(templateFormID, payload);
      } else {
        type === 'risk'
          ? await createNewRA(payload)
          : await createNewTemplate(payload);
      }
      setLoading(false);
      toast.success(`${alertMessage} Published Successfully`);
      setShowConfirmPublishDetailsModal(false);
      navigate(`/risk-assessment${type === 'risk' ? '' : '/template-listing'}`);
    } catch (err) {
      toast.error(`Error while publishing the ${alertMessage}`);
      console.error('Error publishing template:', err);
    }
  };
  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center">
        <div
          className="spinner-border text-primary"
          data-testid="loading-spinner"
          aria-label="Loading"
        />
      </div>
    );
  }
  const getPrimaryBtnTitle = (currentStep: number, lastStep: number) => {
    if (currentStep === lastStep) {
      return type === 'risk' ? 'Preview' : 'Preview Template';
    }
    return 'Next';
  };

  return (
    <>
      {loader && <Loader isOverlayLoader />}
      {!openPreview && (
        <GenericStepper
          breadCrumbTitle={
            type === 'risk'
              ? 'Creating Risk Assessment'
              : 'Creating Risk Assessment Template'
          }
          steps={steps}
          onNext={handleNext}
          onClose={() => {
            let pathSegment: string;
            if (templateFormID) {
              pathSegment = '/drafts';
            } else if (type === 'risk') {
              pathSegment = '';
            } else {
              pathSegment = '/template-listing';
            }

            navigate(`/risk-assessment${pathSegment}`);
          }}
          primaryBtnOnClick={handlePreview}
          secondaryBtnOnClick={handleSaveToDraft}
          primaryBtnTitle={getPrimaryBtnTitle}
          secondaryBtnTitle="Save to Draft"
          primaryBtnDisabled={!stepValid}
          secondaryBtnDisabled={
            !(
              form?.task_requiring_ra &&
              form.task_requiring_ra.trim().length > 0
            )
          }
          onStepChange={handleNext}
          defaultLoadStep={loadStep}
        />
      )}
      {openPreview && (
        <PreviewFormDetails
          form={form}
          setForm={setForm as any}
          atRiskRef={atRiskRef}
          handlePreviewPublush={() => {
            type === 'risk'
              ? handelFormPublish([])
              : setShowConfirmPublishDetailsModal(true);
          }}
          handleSaveToDraft={handleSaveToDraft}
          type={type}
        />
      )}
      {showConfirmPublishDetailsModal && (
        <ConfirmPublishDetailsModal
          onClose={() => {
            setShowConfirmPublishDetailsModal(false);
          }}
          keywords={keywords}
          onSave={handelFormPublish}
        />
      )}
    </>
  );
};
