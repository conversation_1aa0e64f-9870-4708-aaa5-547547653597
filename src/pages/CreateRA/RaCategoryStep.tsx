import React, {forwardRef, useImperativeHandle} from 'react';
import SelectableCheckboxGrid from '../../components/SelectableCheckboxGrid';
import {TemplateForm} from '../../types/template';
import {RiskForm} from '../../types/risk';
import {useDataStoreContext} from '../../context';

export const RaCategoryStep = forwardRef(
  (
    {
      form,
      setForm,
      onValidate,
      isEdit = false,
      type = 'template',
    }: {
      form: TemplateForm | RiskForm;
      setForm: any;
      onValidate?: (valid: boolean) => void;
      isEdit?: boolean;
      type?: 'template' | 'risk';
    },
    ref,
  ) => {
    const {
      dataStore: {riskCategoryList},
    } = useDataStoreContext();

    // Validation logic
    const validate = () => {
      let selected: number[] = [];
      let isOthers = false;
      let othersText = '';

      if (type === 'risk') {
        const riskForm = form as RiskForm;
        selected = riskForm?.risk_category?.category_id || [];
        isOthers = riskForm?.risk_category?.is_other ?? false;
        othersText = riskForm?.risk_category?.value || '';
      } else {
        const templateForm = form as TemplateForm;
        selected = templateForm?.template_category?.category_id || [];
        isOthers = templateForm?.template_category?.is_other ?? false;
        othersText = templateForm?.template_category?.value || '';
      }

      let valid = selected.length > 0 || isOthers;
      if (isOthers) {
        valid = !!othersText.trim();
      }
      if (onValidate) onValidate(valid);
      return valid;
    };

    useImperativeHandle(ref, () => ({
      validate,
    }));

    // Extract dependencies for useEffect
    const categoryIds =
      type === 'risk'
        ? (form as RiskForm).risk_category?.category_id
        : (form as TemplateForm).template_category?.category_id;
    const isOther =
      type === 'risk'
        ? (form as RiskForm).risk_category?.is_other
        : (form as TemplateForm).template_category?.is_other;
    const categoryValue =
      type === 'risk'
        ? (form as RiskForm).risk_category?.value
        : (form as TemplateForm).template_category?.value;

    // Validate on every relevant form change
    React.useEffect(() => {
      validate();
      // eslint-disable-next-line
    }, [categoryIds, isOther, categoryValue]);

    const handleOthersChange = (flag: boolean, value: string) => {
      if (type === 'risk') {
        setForm((prev: RiskForm) => ({
          ...prev,
          risk_category: {
            ...prev.risk_category,
            is_other: flag,
            value: flag ? value : '',
          },
        }));
      } else {
        setForm((prev: TemplateForm) => ({
          ...prev,
          template_category: {
            ...prev.template_category,
            is_other: flag,
            value: flag ? value : '',
          },
        }));
      }
    };

    const handleCheckedChange = (ids: number[]) => {
      if (type === 'risk') {
        setForm((prev: RiskForm) => ({
          ...prev,
          risk_category: {
            ...prev.risk_category,
            category_id: ids,
          },
        }));
      } else {
        setForm((prev: TemplateForm) => ({
          ...prev,
          template_category: {
            ...prev.template_category,
            category_id: ids,
          },
        }));
      }
    };

    // Get the appropriate values based on form type
    const getCategoryData = () => {
      if (type === 'risk') {
        const riskForm = form as RiskForm;
        return {
          initialChecked: riskForm?.risk_category?.category_id || [],
          isOthersSelected: riskForm?.risk_category?.is_other || false,
          othersText: riskForm?.risk_category?.value || '',
        };
      } else {
        const templateForm = form as TemplateForm;
        return {
          initialChecked: templateForm?.template_category?.category_id || [],
          isOthersSelected: templateForm?.template_category?.is_other || false,
          othersText: templateForm?.template_category?.value || '',
        };
      }
    };

    const categoryData = getCategoryData();

    return (
      <SelectableCheckboxGrid
        title={form?.task_requiring_ra || ''}
        subtitle="Select all the R.A. Category"
        searchPlaceholder="Search RA Category"
        options={riskCategoryList}
        initialChecked={categoryData.initialChecked}
        isOthersSelected={categoryData.isOthersSelected}
        othersText={categoryData.othersText}
        onOthersChange={handleOthersChange}
        onChange={handleCheckedChange}
        hasOthers={true}
        isEdit={isEdit}
        dateOfRiskAssessment={
          type === 'risk' ? (form as RiskForm)?.date_risk_assessment ?? '' : ''
        }
        feildType="category"
        type={type}
      />
    );
  },
);

RaCategoryStep.displayName = 'RaCategoryStep';
