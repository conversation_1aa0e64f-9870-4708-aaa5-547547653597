import React, {createContext, useState, useMemo} from 'react';
import {useContextWrapper} from '../hooks';
import {
  CrewMember,
  IdName,
  OfficeItem,
  UserRoleControllerConfig,
  VesselData,
} from '../types';
import {useNavigate} from 'react-router-dom';

interface IDataStoreContext {
  roleConfig: UserRoleControllerConfig;
  dataStore: {
    riskCategoryList: RiskCategory[] | [];
    hazardsList: RiskCategory[] | [];
    riskParameterType: RiskParameter[] | [];
    riskParameterList: RiskCategory[] | [];
    taskReliabilityAssessList: TaskReliabilityAssessList[] | [];
    riskParameterListForRiskRaiting: RiskCategory[] | [];
    vesselListForRisk: VesselData[] | [];
    officeListForRisk: OfficeItem[] | [];
    approversReqListForRisk: IdName[] | [];
    crewMembersListForRisk: CrewMember[] | [];
  };
  setDataStore: React.Dispatch<
    React.SetStateAction<IDataStoreContext['dataStore']>
  >;
  ga4EventTrigger: any;
}

export interface INoNNullableDataStoreContext extends IDataStoreContext {
  dataStore: IDataStoreContext['dataStore'];
}

type DataStoreContextUnion = IDataStoreContext | INoNNullableDataStoreContext;

export type RiskCategory = {
  id: number;
  name: string;
};

export type RiskParameter = {
  id: number;
  name: string;
  parameters: {
    id: number;
    name: string;
  }[];
};
export type TaskReliabilityAssessList = {
  id: number;
  name: string;
  options: string[];
};

const initialState = {
  dataStore: {
    riskCategoryList: [] as RiskCategory[],
    hazardsList: [] as RiskCategory[],
    riskParameterType: [] as RiskParameter[],
    taskReliabilityAssessList: [] as TaskReliabilityAssessList[],
    riskParameterList: [] as RiskCategory[],
    riskParameterListForRiskRaiting: [] as RiskCategory[],
    vesselListForRisk: [] as VesselData[],
    officeListForRisk: [] as OfficeItem[],
    approversReqListForRisk: [] as IdName[],
    crewMembersListForRisk: [] as CrewMember[],
  },
};

export const DataStoreContext = createContext<IDataStoreContext | null>(null);

interface Props {
  roleConfig: UserRoleControllerConfig;
  ga4EventTrigger: any;
}

export const useDataStoreContext = <
  T extends DataStoreContextUnion = IDataStoreContext,
>() =>
  useContextWrapper(DataStoreContext, {
    contextName: useDataStoreContext.name,
    providerName: DataStoreProvider.name,
  }) as T;

const DataStoreProvider = (props: React.PropsWithChildren<Props>) => {
  const navigate = useNavigate();
  const {roleConfig, ga4EventTrigger} = props;
  const {
    riskAssessment: {hasPermision: hasRAViewAccess},
  } = roleConfig;
  if (!hasRAViewAccess) {
    navigate('/home');
  }
  const [dataStore, setDataStore] = useState<IDataStoreContext['dataStore']>(
    initialState['dataStore'],
  );

  const value = useMemo(
    () => ({dataStore, setDataStore, roleConfig, ga4EventTrigger}),
    [dataStore, roleConfig, ga4EventTrigger],
  );

  return (
    <DataStoreContext.Provider value={value}>
      {props.children}
    </DataStoreContext.Provider>
  );
};

export default DataStoreProvider;
