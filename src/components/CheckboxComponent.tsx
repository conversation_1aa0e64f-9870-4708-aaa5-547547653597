import React from 'react';
import {CheckFilled, CheckUnFilled} from '../utils/svgIcons';
import classNames from 'classnames';

const CheckboxComponent = ({
  checked,
  label,
  onChange,
  id,
  className,
}: {
  checked: boolean;
  label?: string | React.ReactNode;
  onChange: () => void;
  id: string;
  className?: string;
}) => (
  // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
  <label
    htmlFor={id}
    className={classNames('ra-form-check-box', className)}
    style={{
      display: 'flex',
      alignItems: 'center',
      minHeight: 20,
      cursor: 'pointer',
      fontSize: 16,
      color: '#333333',
      fontWeight: 400,
      gap: 8,
      userSelect: 'none',
    }}
    // sonar-ignore: label is associated with checkbox, safe to suppress
    onClick={e => e.stopPropagation()} // Prevent click from bubbling to parent
  >
    <span
      style={{
        width: 20,
        height: 20,
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {checked ? <CheckFilled /> : <CheckUnFilled />}
    </span>
    <input
      type="checkbox"
      id={id}
      checked={checked}
      onChange={event => {
        event.stopPropagation();
        onChange();
      }}
      onClick={event => {
        event.stopPropagation();
      }}
      style={{display: 'none'}}
    />
    {label && <span style={{lineHeight: '20px'}}>{label}</span>}
  </label>
);
export default CheckboxComponent;
