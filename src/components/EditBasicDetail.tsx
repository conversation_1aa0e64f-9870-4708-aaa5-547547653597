import React, {useState, useEffect} from 'react';
import {Col, Form, Row} from 'react-bootstrap';
import {TemplateForm} from '../types/template';
import {RiskForm} from '../types/risk';
import DropdownTypeahead from './DropdownTypeahead';
import CustomDatePicker from './CustomDatePicker';
import {
  getApprovalsRequiredList,
  getOfficesList,
  getVesselsList,
} from '../services/services';
import {assessorOptions} from '../pages/CreateRA/BasicDetails';
import {formatDateToYYYYMMDD} from '../utils/helper';

const EditBasicDetailsComp = ({
  clonedForm,
  setClonedForm,
  type = 'template',
}: {
  clonedForm: TemplateForm | RiskForm;
  setClonedForm: any;
  type?: 'template' | 'risk';
}) => {
  const errorMsg = 'This is a mandatory field. Please fill to process.';
  const [vesselOptions, setVesselOptions] = useState<
    {value: number; label: string; vesselId: number}[]
  >([]);
  const [officeOptions, setOfficeOptions] = useState<
    {value: number; label: string}[]
  >([]);
  const [approvalOptions, setApprovalOptions] = useState<
    {value: number; label: string}[]
  >([]);

  // Load vessel and office options for risk forms
  useEffect(() => {
    if (type === 'risk') {
      const loadOptions = async () => {
        try {
          const [vesselResponse, officeResponse, approvalList] =
            await Promise.all([
              getVesselsList(),
              getOfficesList(),
              getApprovalsRequiredList(),
            ]);
          setVesselOptions(
            vesselResponse.map(item => ({
              value: item.id,
              label: item.name,
              vesselId: item.vessel.id,
            })),
          );

          setOfficeOptions(
            officeResponse.map(item => ({
              value: item.id,
              label: item.value,
            })),
          );

          setApprovalOptions(
            approvalList.map(item => ({
              value: item.id,
              label: item.name,
            })),
          );
        } catch (error) {
          console.error('Error loading vessel/office options:', error);
        }
      };

      loadOptions();
    }
  }, [type]);

  // Handle dropdown changes for risk form
  const handleDropdownChange = (name: string, value: any) => {
    setClonedForm({
      ...clonedForm,
      [name]: value,
    });
  };

  // Handle vessel/office dropdown change
  const handleVesselOfficeChange = (selected: any) => {
    const isArray = Array.isArray(selected);
    const selectedItem = isArray ? selected[0] : selected;

    const value = selectedItem?.value ?? 0;
    const vesselId = selectedItem?.vesselId;

    // Update both vessel_ownership_id and vessel_id for risk forms
    setClonedForm({
      ...clonedForm,
      vessel_ownership_id: value,
      ...(vesselId && {vessel_id: vesselId}),
    });
  };

  // Handle approval required dropdown change
  const handleApprovalChange = (selected: any) => {
    let values: string[] = [];

    if (Array.isArray(selected)) {
      values = selected.map((item: any) => item.value);
    } else if (selected) {
      values = [selected.value];
    }

    setClonedForm({
      ...clonedForm,
      approval_required: values,
    });
  };

  // Handle date changes for risk form
  const handleDateChange = (name: string, date?: Date) => {
    setClonedForm({
      ...clonedForm,
      [name]: date ? formatDateToYYYYMMDD(date) : '',
    });
  };

  return (
    <>
      <Row className="mb-3">
        <Col>
          <Form.Group controlId="taskRequiringRa">
            <Form.Label className="fs-14 fw-500">
              Task Requiring R.A.
            </Form.Label>
            <Form.Control
              type="text"
              value={clonedForm.task_requiring_ra}
              onChange={e => {
                setClonedForm({
                  ...clonedForm,
                  task_requiring_ra: e.target.value,
                });
              }}
              maxLength={255}
              className="fs-14"
              isInvalid={clonedForm.task_requiring_ra.trim() === ''}
            />
            <Form.Control.Feedback type="invalid">
              {errorMsg}
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
      </Row>

      {/* Risk Form specific fields */}
      {type === 'risk' && (
        <>
          <Row className="mb-3">
            <Col md={6}>
              <DropdownTypeahead
                label="Assessor"
                options={assessorOptions}
                selected={assessorOptions.filter(
                  opt => opt.value === (clonedForm as RiskForm).assessor,
                )}
                onChange={selected => {
                  const value = Array.isArray(selected)
                    ? (selected[0] as any)?.value
                    : (selected as any)?.value;
                  handleDropdownChange('assessor', value);
                }}
                isInvalid={!(clonedForm as RiskForm).assessor}
                errorMessage={errorMsg}
              />
            </Col>
            <Col md={6}>
              <DropdownTypeahead
                label="Vessel/Office"
                options={
                  (clonedForm as RiskForm).assessor === 2
                    ? vesselOptions
                    : officeOptions
                }
                selected={
                  (clonedForm as RiskForm).assessor === 2
                    ? vesselOptions.filter(
                        opt =>
                          opt.value ===
                          (clonedForm as RiskForm).vessel_ownership_id,
                      )
                    : officeOptions.filter(
                        opt =>
                          opt.value ===
                          (clonedForm as RiskForm).vessel_ownership_id,
                      )
                }
                onChange={handleVesselOfficeChange}
                isInvalid={!(clonedForm as RiskForm).vessel_ownership_id}
                errorMessage={errorMsg}
              />
            </Col>
          </Row>
          <Row className="mb-3">
            <Col md={6}>
              <CustomDatePicker
                label="Date of Risk Assessment"
                value={
                  (clonedForm as RiskForm).date_risk_assessment
                    ? new Date((clonedForm as RiskForm).date_risk_assessment)
                    : undefined
                }
                onChange={date =>
                  handleDateChange('date_risk_assessment', date)
                }
                placeholder="Select Date"
                controlId="date_risk_assessment"
                isRequired={true}
                errorMsg={
                  !(clonedForm as RiskForm).date_risk_assessment ? errorMsg : ''
                }
                minDate={undefined}
              />
            </Col>
            <Col md={6}>
              <DropdownTypeahead
                label="Approvals Required (if necessary)"
                options={approvalOptions}
                selected={approvalOptions.filter(opt =>
                  (clonedForm as RiskForm).approval_required?.includes(
                    opt.value,
                  ),
                )}
                onChange={handleApprovalChange}
                multiple={true}
                isInvalid={
                  !(clonedForm as RiskForm).approval_required ||
                  (clonedForm as RiskForm).approval_required.length === 0
                }
                errorMessage={errorMsg}
              />
            </Col>
          </Row>
        </>
      )}

      <Row>
        <Col>
          <Form.Group controlId="taskDuration">
            <Form.Label className="fs-14 fw-500">Task Duration</Form.Label>
            <Form.Control
              type="text"
              value={clonedForm.task_duration}
              onChange={e => {
                setClonedForm({
                  ...clonedForm,
                  task_duration: e.target.value,
                });
              }}
              maxLength={255}
              placeholder="Enter No. of Days Required"
              isInvalid={clonedForm.task_duration.trim() === ''}
            />
            <Form.Control.Feedback type="invalid">
              {errorMsg}
            </Form.Control.Feedback>
          </Form.Group>
          <Form.Text className="text-muted fs-14">
            Mention if values are in Days/Hours
          </Form.Text>
        </Col>
      </Row>
    </>
  );
};

export default EditBasicDetailsComp;
