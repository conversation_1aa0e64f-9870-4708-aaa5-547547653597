<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftsListing.test.tsx">
        <testCase name="RADraftsListing Component Component Rendering renders the component correctly" duration="73"/>
        <testCase name="RADraftsListing Component Component Rendering renders with correct initial state" duration="14"/>
        <testCase name="RADraftsListing Component Component Rendering renders table columns correctly" duration="10"/>
        <testCase name="RADraftsListing Component Component Rendering displays loading state correctly" duration="10"/>
        <testCase name="RADraftsListing Component Component Rendering displays fetching next page state correctly" duration="10"/>
        <testCase name="RADraftsListing Component Tab Switching switches to RA Template tab correctly" duration="18"/>
        <testCase name="RADraftsListing Component Tab Switching switches back to Risk Assessment tab correctly" duration="19"/>
        <testCase name="RADraftsListing Component Draft Actions renders action dropdown for each draft" duration="9"/>
        <testCase name="RADraftsListing Component Draft Actions displays correct action buttons text" duration="11"/>
        <testCase name="RADraftsListing Component Draft Actions shows action dropdown structure correctly" duration="10"/>
        <testCase name="RADraftsListing Component Draft Actions handles edit action correctly for risk assessment tab" duration="6"/>
        <testCase name="RADraftsListing Component Draft Actions handles edit action correctly for template tab" duration="10"/>
        <testCase name="RADraftsListing Component Draft Actions handles discard action correctly" duration="28"/>
        <testCase name="RADraftsListing Component Draft Actions handles discard action for second draft correctly" duration="17"/>
        <testCase name="RADraftsListing Component Modal Interactions does not show modal initially" duration="52"/>
        <testCase name="RADraftsListing Component Modal Interactions shows modal when discard is clicked" duration="24"/>
        <testCase name="RADraftsListing Component Modal Interactions handles modal close with refetch correctly" duration="17"/>
        <testCase name="RADraftsListing Component Modal Interactions handles modal close without refetch correctly" duration="16"/>
        <testCase name="RADraftsListing Component Modal Interactions shows dropdown menus correctly" duration="10"/>
        <testCase name="RADraftsListing Component Modal Interactions renders three dots icon correctly" duration="11"/>
        <testCase name="RADraftsListing Component Modal Interactions renders dropdown structure correctly" duration="10"/>
        <testCase name="RADraftsListing Component Table Functionality calls fetchNextPage when load more is clicked" duration="5"/>
        <testCase name="RADraftsListing Component Table Functionality displays correct sorting information" duration="11"/>
        <testCase name="RADraftsListing Component Table Functionality handles empty data correctly" duration="11"/>
        <testCase name="RADraftsListing Component Data Display displays draft data correctly" duration="8"/>
        <testCase name="RADraftsListing Component Data Display displays action dropdowns for each draft" duration="33"/>
        <testCase name="RADraftsListing Component Data Display displays table rows correctly" duration="17"/>
        <testCase name="RADraftsListing Component Hook Integration calls useInfiniteQuery with correct parameters for risk tab" duration="11"/>
        <testCase name="RADraftsListing Component Hook Integration uses correct fetch function for risk tab" duration="10"/>
        <testCase name="RADraftsListing Component Hook Integration uses correct fetch function for template tab" duration="7"/>
        <testCase name="getColumns Function Column Configuration returns correct number of columns" duration="0"/>
        <testCase name="getColumns Function Column Configuration configures task_requiring_ra column correctly" duration="0"/>
        <testCase name="getColumns Function Column Configuration configures updated_at column correctly" duration="0"/>
        <testCase name="getColumns Function Column Configuration configures action column correctly" duration="0"/>
        <testCase name="getColumns Function Column Configuration updates header with different total counts" duration="0"/>
        <testCase name="getColumns Function Column Configuration handles zero total count" duration="0"/>
        <testCase name="getColumns Function Cell Renderers renders task_requiring_ra cell correctly" duration="2"/>
        <testCase name="getColumns Function Cell Renderers renders updated_at cell correctly" duration="0"/>
        <testCase name="getColumns Function Cell Renderers renders action cell correctly" duration="1"/>
        <testCase name="getColumns Function Action Handlers generates correct navigation path for risk tab" duration="0"/>
        <testCase name="getColumns Function Action Handlers generates correct navigation path for template tab" duration="0"/>
        <testCase name="getColumns Function Action Handlers calls handleDiscardDraft when discard is clicked" duration="0"/>
        <testCase name="getColumns Function Column Dependencies works with different selectedTab values" duration="0"/>
        <testCase name="getColumns Function Column Dependencies passes all required parameters to column functions" duration="1"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/SearchUserDropdown.test.tsx">
        <testCase name="SearchUserDropdown renders with placeholder when no users are selected" duration="73"/>
        <testCase name="SearchUserDropdown opens dropdown on click and displays all users" duration="55"/>
        <testCase name="SearchUserDropdown filters users based on search input" duration="17"/>
        <testCase name="SearchUserDropdown calls onChange when a user is selected" duration="19"/>
        <testCase name="SearchUserDropdown closes dropdown when clicking outside" duration="61"/>
        <testCase name="SearchUserDropdown Custom placeholder renders with custom placeholder" duration="22"/>
        <testCase name="SearchUserDropdown Selected users display displays selected user names" duration="13"/>
        <testCase name="SearchUserDropdown Selected users display displays counter when more than maxDisplayNames are selected" duration="6"/>
        <testCase name="SearchUserDropdown Selected users display shows tooltip with all selected user names" duration="19"/>
        <testCase name="SearchUserDropdown User deselection deselects a user when clicked again" duration="30"/>
        <testCase name="SearchUserDropdown Select all functionality deselects all when all are already selected" duration="44"/>
        <testCase name="SearchUserDropdown Select all functionality selects only filtered users when search is active" duration="32"/>
        <testCase name="SearchUserDropdown Search functionality filters users by email" duration="22"/>
        <testCase name="SearchUserDropdown Search functionality filters users by designation" duration="14"/>
        <testCase name="SearchUserDropdown Search functionality shows &quot;No users found&quot; when search returns no results" duration="20"/>
        <testCase name="SearchUserDropdown Search functionality handles case-insensitive search" duration="10"/>
        <testCase name="SearchUserDropdown Keyboard accessibility opens dropdown on Enter key" duration="23"/>
        <testCase name="SearchUserDropdown Keyboard accessibility opens dropdown on Space key" duration="17"/>
        <testCase name="SearchUserDropdown Keyboard accessibility closes dropdown on Escape key" duration="15"/>
        <testCase name="SearchUserDropdown User initials generation displays correct initials for users" duration="19"/>
        <testCase name="SearchUserDropdown Edge cases handles empty options array" duration="21"/>
        <testCase name="SearchUserDropdown Edge cases handles users without designation" duration="12"/>
        <testCase name="SearchUserDropdown Edge cases handles invalid user IDs in value prop" duration="6"/>
        <testCase name="SearchUserDropdown Edge cases handles undefined/null search values" duration="15"/>
        <testCase name="SearchUserDropdown Component lifecycle does not call onChange when component mounts with selected users" duration="3"/>
        <testCase name="SearchUserDropdown Component lifecycle handles window resize events" duration="9"/>
        <testCase name="SearchUserDropdown Accessibility has proper ARIA attributes" duration="20"/>
        <testCase name="SearchUserDropdown Accessibility updates aria-expanded when dropdown opens" duration="9"/>
        <testCase name="SearchUserDropdown Accessibility has proper checkbox roles and states" duration="14"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/components/MostlyUsedCard.test.tsx">
        <testCase name="MostlyUsedCard Component Rendering renders the component with all required elements" duration="147"/>
        <testCase name="MostlyUsedCard Component Rendering has the correct CSS class structure" duration="22"/>
        <testCase name="MostlyUsedCard Component Rendering renders menu dots correctly" duration="5"/>
        <testCase name="MostlyUsedCard Template Name Display displays template name through TruncateText component" duration="8"/>
        <testCase name="MostlyUsedCard Template Name Display passes correct maxLength to TruncateText component" duration="9"/>
        <testCase name="MostlyUsedCard Template Name Display handles empty template name" duration="4"/>
        <testCase name="MostlyUsedCard Categories Display displays risk categories count correctly" duration="11"/>
        <testCase name="MostlyUsedCard Categories Display displays hazard categories count correctly" duration="7"/>
        <testCase name="MostlyUsedCard Categories Display handles zero categories" duration="39"/>
        <testCase name="MostlyUsedCard Categories Display handles large category numbers" duration="13"/>
        <testCase name="MostlyUsedCard Keywords Display displays keywords count in label" duration="5"/>
        <testCase name="MostlyUsedCard Keywords Display passes keywords to SingleBadgePopover component" duration="5"/>
        <testCase name="MostlyUsedCard Keywords Display handles empty keywords array" duration="11"/>
        <testCase name="MostlyUsedCard Keywords Display handles single keyword" duration="18"/>
        <testCase name="MostlyUsedCard Keywords Display handles keywords with special characters" duration="9"/>
        <testCase name="MostlyUsedCard Date Display displays formatted creation date" duration="10"/>
        <testCase name="MostlyUsedCard Date Display handles different date formats" duration="17"/>
        <testCase name="MostlyUsedCard Date Display handles invalid date gracefully" duration="6"/>
        <testCase name="MostlyUsedCard Date Display handles empty date string" duration="12"/>
        <testCase name="MostlyUsedCard User Avatar displays initials of username in avatar" duration="7"/>
        <testCase name="MostlyUsedCard User Avatar handles single character username" duration="5"/>
        <testCase name="MostlyUsedCard User Avatar handles lowercase username" duration="8"/>
        <testCase name="MostlyUsedCard User Avatar handles username with special characters" duration="5"/>
        <testCase name="MostlyUsedCard User Avatar handles empty username gracefully" duration="17"/>
        <testCase name="MostlyUsedCard Props Variations handles all minimum values" duration="7"/>
        <testCase name="MostlyUsedCard Props Variations handles all maximum realistic values" duration="14"/>
        <testCase name="MostlyUsedCard Component Structure and Accessibility maintains proper DOM structure" duration="17"/>
        <testCase name="MostlyUsedCard Component Structure and Accessibility has proper avatar structure with menu dots" duration="12"/>
        <testCase name="MostlyUsedCard Integration with Dependencies calls parseDate utility with correct parameters" duration="22"/>
        <testCase name="MostlyUsedCard Integration with Dependencies passes correct props to TruncateText component" duration="12"/>
        <testCase name="MostlyUsedCard Integration with Dependencies passes correct props to SingleBadgePopover component" duration="5"/>
        <testCase name="MostlyUsedCard Optional Props applies custom className when provided" duration="8"/>
        <testCase name="MostlyUsedCard Optional Props passes menuAlign prop to ActionDropdownMenu" duration="17"/>
        <testCase name="MostlyUsedCard Optional Props passes menuAlign horizontal to ActionDropdownMenu" duration="7"/>
        <testCase name="MostlyUsedCard Optional Props hides menu when hideMenu is true" duration="11"/>
        <testCase name="MostlyUsedCard Optional Props shows menu when hideMenu is false" duration="7"/>
        <testCase name="MostlyUsedCard Optional Props shows menu by default when hideMenu is not provided" duration="5"/>
        <testCase name="MostlyUsedCard ActionDropdownMenu Data Structure passes correct data structure to ActionDropdownMenu" duration="7"/>
        <testCase name="MostlyUsedCard ActionDropdownMenu Data Structure passes userDetails with correct structure to ActionDropdownMenu" duration="8"/>
        <testCase name="MostlyUsedCardList Loading State displays loading state when data is being fetched" duration="6"/>
        <testCase name="MostlyUsedCardList Empty State returns null when no data is available" duration="7"/>
        <testCase name="MostlyUsedCardList Empty State returns null when results array is empty" duration="5"/>
        <testCase name="MostlyUsedCardList Success State renders title and cards container" duration="72"/>
        <testCase name="MostlyUsedCardList Success State renders MostlyUsedCard components for each template" duration="7"/>
        <testCase name="MostlyUsedCardList Success State limits display to maximum 4 templates" duration="17"/>
        <testCase name="MostlyUsedCardList Success State passes correct props to MostlyUsedCard components" duration="9"/>
        <testCase name="MostlyUsedCardList Success State handles user details mapping correctly" duration="6"/>
        <testCase name="MostlyUsedCardList Success State falls back to created_by when user details not found" duration="16"/>
        <testCase name="MostlyUsedCardList Success State passes menuAlign as vertical to cards" duration="17"/>
        <testCase name="MostlyUsedCardList Error Handling calls toast.error when onError is triggered" duration="9"/>
        <testCase name="MostlyUsedCardList Error Handling calls toast.error with default message when error has no message" duration="9"/>
        <testCase name="MostlyUsedCardList Hook Integration calls useQuery with correct parameters" duration="6"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/PreviewFormDetails.test.tsx">
        <testCase name="PreviewFormDetails Component Component Rendering renders the component with all main sections" duration="119"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays task title correctly" duration="83"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays task duration correctly" duration="25"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays default task title when not provided" duration="54"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays dash when task duration is not provided" duration="16"/>
        <testCase name="PreviewFormDetails Component Component Rendering renders infinite scroll table with correct data" duration="21"/>
        <testCase name="PreviewFormDetails Component Component Rendering renders table with empty data when no jobs" duration="24"/>
        <testCase name="PreviewFormDetails Component Breadcrumb Navigation renders breadcrumb with correct items" duration="31"/>
        <testCase name="PreviewFormDetails Component Breadcrumb Navigation renders empty breadcrumb item when task title is empty" duration="17"/>
        <testCase name="PreviewFormDetails Component Form Input Interactions handles alternative consideration textarea change" duration="23"/>
        <testCase name="PreviewFormDetails Component Form Input Interactions handles rejection reason textarea change" duration="43"/>
        <testCase name="PreviewFormDetails Component Form Input Interactions displays current values in textareas" duration="21"/>
        <testCase name="PreviewFormDetails Component Button Interactions calls handlePreviewPublish when Publish Template button is clicked" duration="17"/>
        <testCase name="PreviewFormDetails Component Button Interactions calls handleSaveToDraft when Save to Draft button is clicked" duration="8"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens guidance PDF when Guidance Table button is clicked" duration="14"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens risk matrix PDF when Risk Matrix Table button is clicked" duration="11"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens guidance table PDF when button is clicked" duration="12"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens risk matrix PDF when button is clicked" duration="16"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display displays risk categories as badges" duration="18"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display handles categories with is_other flag" duration="26"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display handles hazards with is_other flag" duration="17"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display displays empty categories when no categories selected" duration="23"/>
        <testCase name="PreviewFormDetails Component At Risk Parameters Display displays parameter names in uppercase" duration="40"/>
        <testCase name="PreviewFormDetails Component At Risk Parameters Display handles parameters with custom values" duration="17"/>
        <testCase name="PreviewFormDetails Component At Risk Parameters Display handles empty parameters array" duration="8"/>
        <testCase name="PreviewFormDetails Component Table Configuration renders table with correct column count" duration="11"/>
        <testCase name="PreviewFormDetails Component Table Configuration displays action menu icon in table" duration="14"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling handles undefined form properties gracefully" duration="25"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling handles empty data store gracefully" duration="9"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling handles missing dataStore properties" duration="10"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling displays user name correctly when user is creator/updater" duration="9"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling displays --- when user is not creator/updater" duration="37"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling renders edit buttons with correct icons" duration="30"/>
        <testCase name="PreviewFormDetails Component Component Integration passes correct props to RiskRatingStep" duration="21"/>
        <testCase name="PreviewFormDetails Component Component Integration passes correct props to BottomButton" duration="16"/>
        <testCase name="PreviewFormDetails Component Component Integration renders input components with correct labels" duration="11"/>
        <testCase name="PreviewFormDetails Component Component Integration hides action buttons when previewOnly is true" duration="41"/>
        <testCase name="PreviewFormDetails Component Component Integration shows correct button text for risk type" duration="0"/>
        <testCase name="PreviewFormDetails Component Styling and Layout applies correct styling to main sections" duration="21"/>
        <testCase name="PreviewFormDetails Component Styling and Layout renders badges with correct styling attributes" duration="19"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/RiskRatingStep.test.tsx">
        <testCase name="RiskRatingStep Component Component Rendering renders the component with header by default" duration="205"/>
        <testCase name="RiskRatingStep Component Component Rendering renders without header when disableHeader is true" duration="32"/>
        <testCase name="RiskRatingStep Component Component Rendering renders worst case scenario input" duration="16"/>
        <testCase name="RiskRatingStep Component Component Rendering renders recovery measures input" duration="15"/>
        <testCase name="RiskRatingStep Component Component Rendering renders task reliability assessment section" duration="20"/>
        <testCase name="RiskRatingStep Component Component Rendering renders warning alert" duration="16"/>
        <testCase name="RiskRatingStep Component Risk Rating Calculation displays Medium risk rating when no assessments" duration="13"/>
        <testCase name="RiskRatingStep Component Risk Rating Calculation displays Medium risk rating when all answers are Yes" duration="18"/>
        <testCase name="RiskRatingStep Component Risk Rating Calculation displays High risk rating when all answers are No" duration="20"/>
        <testCase name="RiskRatingStep Component Risk Rating Calculation displays High risk rating when answers are mixed with No" duration="13"/>
        <testCase name="RiskRatingStep Component Form Field Updates updates worst case scenario field" duration="17"/>
        <testCase name="RiskRatingStep Component Form Field Updates updates recovery measures field" duration="37"/>
        <testCase name="RiskRatingStep Component Form Field Updates handles blur events for validation" duration="32"/>
        <testCase name="RiskRatingStep Component Form Field Updates displays form field values correctly" duration="11"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment renders radio options for each question" duration="18"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment shows selected answers correctly" duration="11"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment handles radio button changes" duration="14"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment shows condition input when Yes is selected" duration="10"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment handles condition input changes" duration="12"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment hides condition input when No is selected" duration="5"/>
        <testCase name="RiskRatingStep Component Validation validates required fields are empty" duration="10"/>
        <testCase name="RiskRatingStep Component Validation validates successfully when all fields are filled" duration="14"/>
        <testCase name="RiskRatingStep Component Validation validates task reliability assessment answers" duration="42"/>
        <testCase name="RiskRatingStep Component Edge Cases handles missing task reliability assessment list gracefully" duration="8"/>
        <testCase name="RiskRatingStep Component Edge Cases handles undefined task reliability assessment list" duration="7"/>
        <testCase name="RiskRatingStep Component Edge Cases handles form with empty task name" duration="11"/>
        <testCase name="RiskRatingStep Component Edge Cases handles assessment answer updates correctly" duration="14"/>
        <testCase name="RiskRatingStep Component Edge Cases handles new assessment creation when not exists" duration="13"/>
        <testCase name="RiskRatingStep Component Edge Cases handles condition updates for existing assessments" duration="8"/>
        <testCase name="RiskRatingStep Component Risk Rating Colors displays High risk rating correctly" duration="11"/>
        <testCase name="RiskRatingStep Component Risk Rating Colors displays High risk rating correctly for mixed answers" duration="11"/>
        <testCase name="RiskRatingStep Component Risk Rating Colors displays Medium risk rating correctly for all Yes answers" duration="10"/>
        <testCase name="RiskRatingStep Component Component Ref Methods exposes validate method through ref" duration="26"/>
        <testCase name="RiskRatingStep Component Component Ref Methods validate method returns boolean" duration="24"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/BasicDetails.test.tsx">
        <testCase name="BasicDetails Component Rendering renders the component with all required fields" duration="90"/>
        <testCase name="BasicDetails Component Rendering renders input fields with correct attributes" duration="35"/>
        <testCase name="BasicDetails Component Rendering renders textarea for reason for rejection field" duration="8"/>
        <testCase name="BasicDetails Component Rendering displays form values correctly" duration="13"/>
        <testCase name="BasicDetails Component Form Interactions calls setForm when input values change" duration="174"/>
        <testCase name="BasicDetails Component Form Interactions handles onChange events correctly" duration="7"/>
        <testCase name="BasicDetails Component Form Interactions handles onBlur events correctly" duration="12"/>
        <testCase name="BasicDetails Component Form Interactions updates touched state on blur" duration="9"/>
        <testCase name="BasicDetails Component Form Interactions clears validation error when field is filled" duration="19"/>
        <testCase name="BasicDetails Component Form Interactions handles duration field as string conversion" duration="15"/>
        <testCase name="BasicDetails Component Validation validates all required fields are empty initially" duration="10"/>
        <testCase name="BasicDetails Component Validation validates successfully when all fields are filled" duration="11"/>
        <testCase name="BasicDetails Component Validation fails validation when task_requiring_ra is empty" duration="7"/>
        <testCase name="BasicDetails Component Validation fails validation when task_duration is empty" duration="5"/>
        <testCase name="BasicDetails Component Validation fails validation when task_alternative_consideration is empty" duration="8"/>
        <testCase name="BasicDetails Component Validation fails validation when task_rejection_reason is empty" duration="7"/>
        <testCase name="BasicDetails Component Validation handles whitespace-only values as invalid" duration="7"/>
        <testCase name="BasicDetails Component Validation validates with form containing all required fields" duration="7"/>
        <testCase name="BasicDetails Component Validation handles null task_duration correctly" duration="5"/>
        <testCase name="BasicDetails Component Error Display shows validation errors only after field is touched" duration="9"/>
        <testCase name="BasicDetails Component Error Display displays correct error message for all fields" duration="20"/>
        <testCase name="BasicDetails Component Component Props works without onValidate callback" duration="7"/>
        <testCase name="BasicDetails Component Component Props exposes validate method through ref" duration="6"/>
        <testCase name="BasicDetails Component Component Props handles form updates correctly" duration="9"/>
        <testCase name="BasicDetails Component Field-specific Validation validates individual fields correctly" duration="35"/>
        <testCase name="BasicDetails Component Field-specific Validation handles empty string validation correctly" duration="26"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/utils/helper.test.ts">
        <testCase name="generateGroupedOptions should generate grouped options with default columns" duration="1"/>
        <testCase name="generateGroupedOptions should use custom columns when provided" duration="0"/>
        <testCase name="generateGroupedOptions should handle empty input array" duration="1"/>
        <testCase name="generateGroupedOptions should handle group with empty parameters array" duration="0"/>
        <testCase name="generateGroupedOptions should handle group names with special characters" duration="0"/>
        <testCase name="generateGroupedOptions should handle zero columns" duration="0"/>
        <testCase name="removeAndReindexJobState should remove item at index and reindex remaining items" duration="1"/>
        <testCase name="removeAndReindexJobState should remove first item and reindex correctly" duration="0"/>
        <testCase name="removeAndReindexJobState should remove last item and keep other indices unchanged" duration="0"/>
        <testCase name="removeAndReindexJobState should handle empty state object" duration="0"/>
        <testCase name="removeAndReindexJobState should handle single item removal" duration="1"/>
        <testCase name="removeAndReindexJobState should handle non-existent index gracefully" duration="0"/>
        <testCase name="removeAndReindexJobState should handle non-sequential indices" duration="0"/>
        <testCase name="removeAndReindexJobState should preserve object properties and structure" duration="0"/>
        <testCase name="formParameterHandler should delete template_hazard.value when template_hazard[0].isOther is false and value is empty" duration="0"/>
        <testCase name="formParameterHandler should keep template_hazard.value when template_hazard[0].isOther is true" duration="1"/>
        <testCase name="formParameterHandler should keep template_hazard.value when value is not empty" duration="0"/>
        <testCase name="formParameterHandler should filter parameters based on parameter_id length and is_other flag" duration="0"/>
        <testCase name="formParameterHandler should handle undefined parameters array" duration="0"/>
        <testCase name="formParameterHandler should process template_job array and remove job_id when array exists" duration="1"/>
        <testCase name="formParameterHandler should delete template_job when it is not an array and has no job_step" duration="0"/>
        <testCase name="formParameterHandler should process template_task_reliability_assessment when array has length" duration="0"/>
        <testCase name="formParameterHandler should handle template_task_reliability_assessment with missing properties" duration="0"/>
        <testCase name="formParameterHandler should remove value property from parameters when is_other is false" duration="0"/>
        <testCase name="formParameterHandler should handle complex payload with all features" duration="1"/>
        <testCase name="formParameterHandler should handle null and undefined values gracefully" duration="0"/>
        <testCase name="formParameterHandler should handle empty arrays" duration="0"/>
        <testCase name="formParameterHandler should handle parameters with null values" duration="0"/>
        <testCase name="formParameterHandler should delete template_hazard when hazard_id is empty and is_other is false" duration="0"/>
        <testCase name="formParameterHandler should handle undefined template_job gracefully" duration="0"/>
        <testCase name="formParameterHandler should handle template_job with risk ratings correctly" duration="0"/>
        <testCase name="formParameterHandler should delete template_category when category_id is empty" duration="0"/>
        <testCase name="formParameterHandler should keep template_category when category_id has values" duration="0"/>
        <testCase name="formParameterHandler should handle template_job deletion when first job has empty job_step" duration="0"/>
        <testCase name="createFormFromData should create form with default values when no data provided" duration="1"/>
        <testCase name="createFormFromData should create form with provided data values" duration="6"/>
        <testCase name="createFormFromData should handle empty template_category array" duration="0"/>
        <testCase name="createFormFromData should map template_hazards correctly" duration="1"/>
        <testCase name="createFormFromData should handle template_hazards without other values" duration="0"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/CreateRA.page.test.tsx">
        <testCase name="StepperPage Component Component Rendering renders the component with GenericStepper initially" duration="154"/>
        <testCase name="StepperPage Component Component Rendering renders the first step component correctly" duration="19"/>
        <testCase name="StepperPage Component Component Rendering does not render preview initially" duration="7"/>
        <testCase name="StepperPage Component Component Rendering does not render confirm publish modal initially" duration="6"/>
        <testCase name="StepperPage Component Data Loading loads all required data on component mount" duration="11"/>
        <testCase name="StepperPage Component Data Loading calls setDataStore with loaded data" duration="58"/>
        <testCase name="StepperPage Component Data Loading handles data loading errors gracefully" duration="58"/>
        <testCase name="StepperPage Component Data Loading processes risk parameter data with lodash groupBy" duration="67"/>
        <testCase name="StepperPage Component Data Loading calls getMainRiskParameterType twice with different parameters" duration="16"/>
        <testCase name="StepperPage Component Data Loading handles partial service failures" duration="58"/>
        <testCase name="StepperPage Component Navigation and Button Interactions handles close button click for template creation" duration="14"/>
        <testCase name="StepperPage Component Navigation and Button Interactions handles close button click for risk creation" duration="8"/>
        <testCase name="StepperPage Component Navigation and Button Interactions handles close button click for draft editing" duration="13"/>
        <testCase name="StepperPage Component Navigation and Button Interactions displays correct button titles" duration="8"/>
        <testCase name="StepperPage Component Navigation and Button Interactions displays Preview Template on last step" duration="36"/>
        <testCase name="StepperPage Component Preview Functionality primary button is initially disabled" duration="9"/>
        <testCase name="StepperPage Component Preview Functionality enables primary button after validation" duration="19"/>
        <testCase name="StepperPage Component Preview Functionality shows success toast when handleSave is called during preview" duration="4"/>
        <testCase name="StepperPage Component Preview Functionality calls handlePreview when primary button is clicked" duration="15"/>
        <testCase name="StepperPage Component Preview Functionality handles error in handleSave" duration="7"/>
        <testCase name="StepperPage Component Modal Functionality tests modal components are properly mocked" duration="14"/>
        <testCase name="StepperPage Component Modal Functionality shows preview modal when openPreview is true" duration="13"/>
        <testCase name="StepperPage Component Modal Functionality shows confirm publish modal when handlePreviewPublush is called" duration="33"/>
        <testCase name="StepperPage Component Modal Functionality closes confirm publish modal when onClose is called" duration="9"/>
        <testCase name="StepperPage Component Modal Functionality handles onSave in confirm publish modal" duration="7"/>
        <testCase name="StepperPage Component Step Validation handles next step validation for BasicDetails (step 1)" duration="14"/>
        <testCase name="StepperPage Component Step Validation handles step change validation" duration="25"/>
        <testCase name="StepperPage Component Step Validation validates BasicDetails step (step 1) with successful validation" duration="25"/>
        <testCase name="StepperPage Component Step Validation validates BasicDetails step (step 1) with failed validation" duration="15"/>
        <testCase name="StepperPage Component Step Validation validates RaCategoryStep (step 2)" duration="30"/>
        <testCase name="StepperPage Component Step Validation validates HazardCategoryStep (step 3)" duration="22"/>
        <testCase name="StepperPage Component Step Validation validates other steps (step 4+)" duration="23"/>
        <testCase name="StepperPage Component Form State Management initializes form with default values" duration="4"/>
        <testCase name="StepperPage Component Form State Management renders step components indicating form state is passed" duration="4"/>
        <testCase name="StepperPage Component Form State Management initializes keywords state as empty array" duration="5"/>
        <testCase name="StepperPage Component Form State Management initializes stepValid state as false" duration="5"/>
        <testCase name="StepperPage Component Form State Management initializes openPreview state as false" duration="7"/>
        <testCase name="StepperPage Component Form State Management initializes showConfirmPublishDetailsModal state as false" duration="7"/>
        <testCase name="StepperPage Component Form State Management maintains form structure with all required fields" duration="7"/>
        <testCase name="StepperPage Component Error Handling handles service errors during data loading" duration="64"/>
        <testCase name="StepperPage Component Error Handling logs errors to console during data loading" duration="65"/>
        <testCase name="StepperPage Component Component Integration passes correct props to GenericStepper" duration="6"/>
        <testCase name="StepperPage Component Component Integration renders step components indicating proper integration" duration="5"/>
        <testCase name="StepperPage Component Component Integration handles primary button disabled state correctly" duration="7"/>
        <testCase name="StepperPage Component Component Integration has all required step components configured" duration="6"/>
        <testCase name="StepperPage Component Component Integration configures secondary button correctly" duration="5"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles empty data responses gracefully" duration="61"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles null/undefined data responses" duration="58"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles component unmounting during async operations" duration="10"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles multiple rapid button clicks" duration="40"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles step validation with missing refs" duration="12"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles form state updates during validation" duration="32"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles concurrent API calls" duration="162"/>
        <testCase name="StepperPage Component StepperPage Additional Coverage shows loading spinner when loading" duration="7"/>
        <testCase name="StepperPage Component StepperPage Additional Coverage handles error in handelFormPublish" duration="8"/>
        <testCase name="StepperPage Component StepperPage Additional Coverage handles error in handleSaveToDraft" duration="11"/>
        <testCase name="StepperPage Component StepperPage Additional Coverage handles pathname edge cases (no match)" duration="9"/>
        <testCase name="StepperPage Component StepperPage Additional Coverage handles pathname edge cases (risk-creation)" duration="10"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/components/ActionDropdownMenu.test.tsx">
        <testCase name="ActionDropdownMenu Component Rendering should render the dropdown menu with three dots icon" duration="80"/>
        <testCase name="ActionDropdownMenu Component Rendering should render with correct CSS classes for horizontal alignment by default" duration="11"/>
        <testCase name="ActionDropdownMenu Component Rendering should render with vertical alignment class when menuAlign is vertical" duration="9"/>
        <testCase name="ActionDropdownMenu Component Rendering should render dropdown structure with correct classes" duration="13"/>
        <testCase name="ActionDropdownMenu Menu Items should render dropdown with proper structure" duration="12"/>
        <testCase name="ActionDropdownMenu Menu Items should render component correctly when user has permission" duration="5"/>
        <testCase name="ActionDropdownMenu Menu Items should not render Archive modal when user does not have permission" duration="4"/>
        <testCase name="ActionDropdownMenu Menu Items should contain dropdown menu structure for menu items" duration="4"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should render component structure correctly regardless of modal visibility" duration="4"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle different data configurations without errors" duration="7"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle different user details configurations" duration="5"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle empty user details gracefully" duration="4"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle onSuccess callback prop correctly" duration="4"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should render without onSuccess callback" duration="5"/>
        <testCase name="ActionDropdownMenu Props Handling should handle menuAlign prop correctly" duration="12"/>
        <testCase name="ActionDropdownMenu Props Handling should default to horizontal alignment when menuAlign is not provided" duration="6"/>
        <testCase name="ActionDropdownMenu View Template Action should have dropdown structure that can contain View Template item" duration="4"/>
        <testCase name="ActionDropdownMenu View Template Action should render component without errors" duration="3"/>
        <testCase name="ActionDropdownMenu View Template Action should have proper dropdown structure for menu interactions" duration="4"/>
        <testCase name="ActionDropdownMenu Edge Cases should handle missing template data gracefully" duration="6"/>
        <testCase name="ActionDropdownMenu Edge Cases should handle null/undefined roleConfig gracefully" duration="3"/>
        <testCase name="ActionDropdownMenu Edge Cases should handle missing context gracefully" duration="53"/>
        <testCase name="ActionDropdownMenu Accessibility should have proper ARIA attributes for dropdown" duration="5"/>
        <testCase name="ActionDropdownMenu Accessibility should have focusable dropdown toggle" duration="5"/>
        <testCase name="ActionDropdownMenu Accessibility should have proper dropdown structure for screen readers" duration="6"/>
        <testCase name="ActionDropdownMenu Accessibility should have proper CSS classes for accessibility" duration="4"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/routes/route.config.test.ts">
        <testCase name="route.config IRoute interface should define correct interface structure" duration="2"/>
        <testCase name="route.config IRoute interface should allow optional properties" duration="1"/>
        <testCase name="route.config routesConfig function Basic functionality should return an array of routes" duration="0"/>
        <testCase name="route.config routesConfig function Basic functionality should return routes with correct structure" duration="1"/>
        <testCase name="route.config routesConfig function Route definitions should define template creation route correctly" duration="1"/>
        <testCase name="route.config routesConfig function Route definitions should define template selection route with correct permission" duration="0"/>
        <testCase name="route.config routesConfig function Route definitions should define all StepperPage routes correctly" duration="0"/>
        <testCase name="route.config routesConfig function Permission handling should set isPermission to true when user has permission" duration="2"/>
        <testCase name="route.config routesConfig function Permission handling should set isPermission to false when user lacks permission" duration="0"/>
        <testCase name="route.config routesConfig function Permission handling should use roleConfig.riskAssessment.hasPermision for most routes" duration="0"/>
        <testCase name="route.config routesConfig function Route paths should have correct path structure" duration="0"/>
        <testCase name="route.config routesConfig function Route paths should not have leading slashes in paths" duration="0"/>
        <testCase name="route.config routesConfig function Route paths should have hierarchical path structure" duration="0"/>
        <testCase name="route.config routesConfig function Component assignments should have all components defined" duration="0"/>
        <testCase name="route.config routesConfig function Edge cases and error handling should handle null roleConfig gracefully" duration="10"/>
        <testCase name="route.config routesConfig function Edge cases and error handling should handle undefined roleConfig gracefully" duration="1"/>
        <testCase name="route.config routesConfig function Edge cases and error handling should handle roleConfig without riskAssessment property" duration="0"/>
        <testCase name="route.config routesConfig function Edge cases and error handling should handle roleConfig with null riskAssessment" duration="0"/>
        <testCase name="route.config routesConfig function Return value consistency should return consistent results for same input" duration="0"/>
        <testCase name="route.config routesConfig function Return value consistency should return different results for different permissions" duration="1"/>
        <testCase name="route.config routesConfig function Route configuration completeness should not have redirect properties" duration="0"/>
        <testCase name="route.config routesConfig function Route configuration completeness should not have childRoutes properties" duration="0"/>
        <testCase name="route.config routesConfig function Route configuration completeness should have all required properties for routing" duration="2"/>
        <testCase name="route.config Integration with routing system should be compatible with React Router structure" duration="0"/>
        <testCase name="route.config Integration with routing system should provide all necessary route information" duration="1"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/DiscardDraftModal.test.tsx">
        <testCase name="DiscardDraftModal Component Rendering renders modal with correct title and content" duration="31"/>
        <testCase name="DiscardDraftModal Component Rendering renders modal with correct buttons" duration="39"/>
        <testCase name="DiscardDraftModal Component Rendering renders modal with correct attributes" duration="7"/>
        <testCase name="DiscardDraftModal Component Rendering renders buttons with correct classes" duration="17"/>
        <testCase name="DiscardDraftModal Button States enables both buttons initially" duration="13"/>
        <testCase name="DiscardDraftModal Button States disables both buttons when deleting" duration="150"/>
        <testCase name="DiscardDraftModal Cancel Functionality calls onClose without parameters when cancel is clicked" duration="18"/>
        <testCase name="DiscardDraftModal Cancel Functionality does not call delete services when cancel is clicked" duration="15"/>
        <testCase name="DiscardDraftModal Delete Risk Functionality (activeTab !== 2) calls deleteRiskById when activeTab is 1 and discard is clicked" duration="25"/>
        <testCase name="DiscardDraftModal Delete Risk Functionality (activeTab !== 2) calls deleteRiskById when activeTab is 0 and discard is clicked" duration="22"/>
        <testCase name="DiscardDraftModal Delete Risk Functionality (activeTab !== 2) calls deleteRiskById when activeTab is 3 and discard is clicked" duration="24"/>
        <testCase name="DiscardDraftModal Delete Template Functionality (activeTab === 2) calls deleteTemplateById when activeTab is 2 and discard is clicked" duration="15"/>
        <testCase name="DiscardDraftModal Error Handling handles deleteRiskById error and still calls onClose" duration="15"/>
        <testCase name="DiscardDraftModal Error Handling handles deleteTemplateById error and still calls onClose" duration="21"/>
        <testCase name="DiscardDraftModal Error Handling resets isDeleting state after error" duration="25"/>
        <testCase name="DiscardDraftModal Props Validation works with different id values" duration="13"/>
        <testCase name="DiscardDraftModal Props Validation works with id value 0" duration="16"/>
        <testCase name="DiscardDraftModal Props Validation works with negative id values" duration="13"/>
        <testCase name="DiscardDraftModal Modal Behavior renders modal with show prop set to true" duration="4"/>
        <testCase name="DiscardDraftModal Modal Behavior renders modal body with correct class" duration="6"/>
        <testCase name="DiscardDraftModal Modal Behavior renders modal with proper structure" duration="18"/>
        <testCase name="DiscardDraftModal Accessibility has proper alert role for warning message" duration="3"/>
        <testCase name="DiscardDraftModal Accessibility has proper modal structure" duration="7"/>
        <testCase name="DiscardDraftModal Accessibility buttons are focusable" duration="46"/>
        <testCase name="DiscardDraftModal Integration Tests completes full delete risk workflow" duration="28"/>
        <testCase name="DiscardDraftModal Integration Tests completes full delete template workflow" duration="25"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/components/ArchiveTemplateModal.test.tsx">
        <testCase name="ArchiveTemplateModal Component Rendering renders the trigger element" duration="71"/>
        <testCase name="ArchiveTemplateModal Component Rendering does not show modal initially" duration="34"/>
        <testCase name="ArchiveTemplateModal Component Rendering renders with custom trigger element" duration="5"/>
        <testCase name="ArchiveTemplateModal Modal Functionality opens modal when trigger is clicked" duration="83"/>
        <testCase name="ArchiveTemplateModal Modal Functionality closes modal when Cancel button is clicked" duration="116"/>
        <testCase name="ArchiveTemplateModal Modal Functionality closes modal when clicking outside (onHide)" duration="25"/>
        <testCase name="ArchiveTemplateModal Template Details Display displays template name correctly" duration="20"/>
        <testCase name="ArchiveTemplateModal Template Details Display displays risk categories count" duration="50"/>
        <testCase name="ArchiveTemplateModal Template Details Display displays hazard categories count" duration="63"/>
        <testCase name="ArchiveTemplateModal Template Details Display displays created on date" duration="25"/>
        <testCase name="ArchiveTemplateModal Template Details Display displays keywords through SingleBadgePopover" duration="42"/>
        <testCase name="ArchiveTemplateModal Archive Functionality calls markTemplateAsArchived service when Move to Archive button is clicked" duration="33"/>
        <testCase name="ArchiveTemplateModal Archive Functionality closes modal after successful archiving" duration="35"/>
        <testCase name="ArchiveTemplateModal Archive Functionality calls onSuccess callback after successful archiving" duration="36"/>
        <testCase name="ArchiveTemplateModal Archive Functionality handles archiving error gracefully" duration="36"/>
        <testCase name="ArchiveTemplateModal Props Variations handles empty keywords array" duration="16"/>
        <testCase name="ArchiveTemplateModal Props Variations handles single keyword" duration="13"/>
        <testCase name="ArchiveTemplateModal Props Variations handles zero risk and hazard categories" duration="29"/>
        <testCase name="ArchiveTemplateModal Props Variations handles long template name" duration="21"/>
        <testCase name="ArchiveTemplateModal Props Variations handles special characters in template name" duration="66"/>
        <testCase name="ArchiveTemplateModal Props Variations handles different date formats" duration="18"/>
        <testCase name="ArchiveTemplateModal Modal Structure and Classes applies correct CSS classes to modal" duration="18"/>
        <testCase name="ArchiveTemplateModal Modal Structure and Classes renders modal header correctly" duration="17"/>
        <testCase name="ArchiveTemplateModal Modal Structure and Classes renders alert message with correct styling" duration="15"/>
        <testCase name="ArchiveTemplateModal Modal Structure and Classes renders template detail card structure" duration="17"/>
        <testCase name="ArchiveTemplateModal Modal Structure and Classes renders button group with correct variants" duration="19"/>
        <testCase name="ArchiveTemplateModal Accessibility modal is centered" duration="20"/>
        <testCase name="ArchiveTemplateModal Accessibility has proper modal structure for screen readers" duration="14"/>
        <testCase name="ArchiveTemplateModal Edge Cases handles rapid clicking of trigger" duration="46"/>
        <testCase name="ArchiveTemplateModal Edge Cases handles clicking archive button multiple times" duration="41"/>
        <testCase name="ArchiveTemplateModal Edge Cases handles multiple archive button clicks by closing modal" duration="45"/>
        <testCase name="ArchiveTemplateModal Edge Cases shows loading state during archive operation" duration="158"/>
        <testCase name="ArchiveTemplateModal Edge Cases prevents multiple simultaneous archive operations" duration="45"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/InfiniteScrollTable.test.tsx">
        <testCase name="InfiniteScrollTable Component renders table with data and headers" duration="26"/>
        <testCase name="InfiniteScrollTable Component displays loading spinner when isLoading is true" duration="39"/>
        <testCase name="InfiniteScrollTable Component displays no results message when data is empty" duration="5"/>
        <testCase name="InfiniteScrollTable Component handles sorting when clicking column headers" duration="48"/>
        <testCase name="InfiniteScrollTable Component renders sticky actions column" duration="27"/>
        <testCase name="InfiniteScrollTable Component fetches more data when scrolled to bottom and more pages exist" duration="12"/>
        <testCase name="InfiniteScrollTable Component does not fetch more data when at the last page" duration="117"/>
        <testCase name="InfiniteScrollTable Component does not fetch more data when already fetching" duration="125"/>
        <testCase name="InfiniteScrollTable Component handles the case when all content fits in view and more pages exist" duration="14"/>
        <testCase name="InfiniteScrollTable Component uses correct CSS classes for column styling" duration="28"/>
        <testCase name="InfiniteScrollTable Component applies virtualization to rows" duration="117"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/DropdownTypeahead.test.tsx">
        <testCase name="DropdownTypeahead renders with proper accessibility attributes" duration="74"/>
        <testCase name="DropdownTypeahead hides label when hideLabel prop is true" duration="19"/>
        <testCase name="DropdownTypeahead disables the input when disabled prop is true" duration="23"/>
        <testCase name="DropdownTypeahead shows error message when isInvalid is true" duration="14"/>
        <testCase name="DropdownTypeahead handles single selection correctly" duration="141"/>
        <testCase name="DropdownTypeahead handles multiple selection correctly" duration="78"/>
        <testCase name="DropdownTypeahead calls onInputChange when typing in the input" duration="77"/>
        <testCase name="DropdownTypeahead displays &quot;No results found&quot; when no options match search" duration="147"/>
        <testCase name="DropdownTypeahead handles special option selection" duration="29"/>
        <testCase name="DropdownTypeahead displays token with more count for multiple selection" duration="10"/>
        <testCase name="DropdownTypeahead clears selection when clear button is clicked" duration="21"/>
        <testCase name="DropdownTypeahead toggles dropdown when clear icon is clicked with no selection" duration="26"/>
        <testCase name="DropdownTypeahead renders custom placeholder when disabledSelectPrefix is true" duration="17"/>
        <testCase name="DropdownTypeahead shows required indicator when required prop is true" duration="6"/>
        <testCase name="DropdownTypeahead handles non-object options correctly" duration="29"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/InitialRiskRatingModal.test.tsx">
        <testCase name="InitialRiskRatingModal does not render modal when `show` is false" duration="12"/>
        <testCase name="InitialRiskRatingModal calls onHide when Cancel button is clicked" duration="155"/>
        <testCase name="InitialRiskRatingModal calls onSelect when a tile is clicked" duration="63"/>
        <testCase name="InitialRiskRatingModal highlights selected tile based on selectedValue" duration="33"/>
        <testCase name="InitialRiskRatingModal renders modal with custom title" duration="23"/>
        <testCase name="InitialRiskRatingModal renders modal without title when title prop is not provided" duration="58"/>
        <testCase name="InitialRiskRatingModal renders all consequence rows and likelihood columns" duration="95"/>
        <testCase name="InitialRiskRatingModal renders all risk code tiles" duration="45"/>
        <testCase name="InitialRiskRatingModal updates selected state when selectedValue prop changes" duration="68"/>
        <testCase name="InitialRiskRatingModal handles empty selectedValue prop" duration="86"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality disables tiles with higher risk than irrValue" duration="79"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality tests compareRiskCodes function through different irrValue scenarios" duration="63"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality tests compareRiskCodes with same row different columns" duration="17"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality tests compareRiskCodes with different rows same columns" duration="16"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality handles edge case with no irrValue" duration="89"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality handles empty irrValue" duration="47"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality shows correct tooltip for disabled tiles" duration="17"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality does not call onSelect when disabled tile is clicked" duration="16"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality shows tooltip for significant risk reduction" duration="16"/>
        <testCase name="InitialRiskRatingModal Utility functions getCellColor returns green color for green codes" duration="0"/>
        <testCase name="InitialRiskRatingModal Utility functions getCellColor returns red color for red codes" duration="1"/>
        <testCase name="InitialRiskRatingModal Utility functions getCellColor returns yellow color for other codes" duration="0"/>
        <testCase name="InitialRiskRatingModal Utility functions Edge cases and error handling handles invalid risk codes gracefully" duration="23"/>
        <testCase name="InitialRiskRatingModal Utility functions Edge cases and error handling handles malformed risk codes in comparison" duration="34"/>
        <testCase name="InitialRiskRatingModal Component behavior calls onSelect without onSelect prop" duration="19"/>
        <testCase name="InitialRiskRatingModal Component behavior renders correct tile colors based on getCellColor" duration="19"/>
        <testCase name="InitialRiskRatingModal Component behavior handles modal close via backdrop or escape" duration="25"/>
        <testCase name="InitialRiskRatingModal Component behavior updates internal state when tile is clicked" duration="121"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/AddJobsStep.test.tsx">
        <testCase name="AddJobsStep Component Component Rendering renders the component with task title" duration="212"/>
        <testCase name="AddJobsStep Component Component Rendering renders guidance and risk matrix buttons" duration="32"/>
        <testCase name="AddJobsStep Component Component Rendering renders add job button" duration="112"/>
        <testCase name="AddJobsStep Component Component Rendering renders job cards when jobs exist" duration="27"/>
        <testCase name="AddJobsStep Component Component Rendering renders expand job cards link when multiple jobs exist" duration="49"/>
        <testCase name="AddJobsStep Component Component Rendering displays default task title when task_requiring_ra is empty" duration="63"/>
        <testCase name="AddJobsStep Component Component Rendering renders hazard and control measures section" duration="20"/>
        <testCase name="AddJobsStep Component Job Management expands job card when header is clicked" duration="42"/>
        <testCase name="AddJobsStep Component Job Management shows job step in collapsed header when available" duration="33"/>
        <testCase name="AddJobsStep Component Job Management toggles job card expansion correctly" duration="92"/>
        <testCase name="AddJobsStep Component Job Management deletes job when delete button is clicked" duration="108"/>
        <testCase name="AddJobsStep Component Job Management expands all job cards when expand job cards link is clicked" duration="100"/>
        <testCase name="AddJobsStep Component Form Field Updates updates job step field" duration="102"/>
        <testCase name="AddJobsStep Component Form Field Updates updates job hazard field" duration="30"/>
        <testCase name="AddJobsStep Component Form Field Updates updates nature of risk field" duration="28"/>
        <testCase name="AddJobsStep Component Form Field Updates updates existing control field" duration="30"/>
        <testCase name="AddJobsStep Component Form Field Updates updates additional mitigation field" duration="28"/>
        <testCase name="AddJobsStep Component Form Field Updates displays form field values correctly" duration="27"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality displays initial risk rating section" duration="30"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality displays residual risk rating section" duration="69"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality shows risk parameters for each rating type" duration="27"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality displays existing risk ratings" duration="45"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality opens risk rating modal when initial risk rating is clicked" duration="41"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality opens risk rating modal when residual risk rating is clicked" duration="48"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality handles risk rating selection from modal" duration="31"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality closes modal when close button is clicked" duration="27"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality updates reason for lowering when text is entered" duration="25"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality displays existing reason for lowering" duration="31"/>
        <testCase name="AddJobsStep Component External Links opens guidance table PDF when button is clicked" duration="30"/>
        <testCase name="AddJobsStep Component External Links opens risk matrix PDF when button is clicked" duration="17"/>
        <testCase name="AddJobsStep Component Empty State renders correctly when no jobs exist" duration="9"/>
        <testCase name="AddJobsStep Component Keyboard Navigation handles keyboard navigation for expand job cards link" duration="157"/>
        <testCase name="AddJobsStep Component Edge Cases handles missing risk parameters gracefully" duration="17"/>
        <testCase name="AddJobsStep Component Edge Cases handles job without initial risk rating for residual section" duration="27"/>
        <testCase name="AddJobsStep Component Validation validates required fields correctly" duration="26"/>
        <testCase name="AddJobsStep Component Validation validates empty form correctly" duration="27"/>
        <testCase name="AddJobsStep Component Validation validates form with no jobs" duration="17"/>
        <testCase name="AddJobsStep Component Risk Rating Utilities handles risk rating modal with correct title" duration="25"/>
        <testCase name="AddJobsStep Component Risk Rating Utilities handles residual risk rating modal with correct title" duration="33"/>
        <testCase name="AddJobsStep Component Risk Rating Utilities displays correct IRR value in residual modal" duration="33"/>
        <testCase name="AddJobsStep Component Form Field Interactions handles onBlur events for form fields" duration="33"/>
        <testCase name="AddJobsStep Component Form Field Interactions handles maxLength constraints" duration="26"/>
        <testCase name="AddJobsStep Component Component Props works without onValidate callback" duration="130"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/Drawer.test.tsx">
        <testCase name="Drawer component renders trigger and toggles drawer open/close" duration="29"/>
        <testCase name="Drawer component renders children as function and allows closing via context" duration="7"/>
        <testCase name="Drawer component applies custom className and position" duration="8"/>
        <testCase name="Drawer component hides content when closed via close button" duration="17"/>
        <testCase name="Drawer component Portal behavior renders drawer in portal by default" duration="6"/>
        <testCase name="Drawer component Portal behavior renders drawer without portal when notUsePortal is true" duration="9"/>
        <testCase name="Drawer component Position variants applies end position by default" duration="3"/>
        <testCase name="Drawer component Position variants applies start position when specified" duration="6"/>
        <testCase name="Drawer component Drawer structure and styling has correct CSS classes and structure" duration="9"/>
        <testCase name="Drawer component Drawer structure and styling applies custom className correctly" duration="6"/>
        <testCase name="Drawer component Drawer structure and styling renders close icon with correct styling" duration="7"/>
        <testCase name="Drawer component Children rendering renders static children correctly" duration="10"/>
        <testCase name="Drawer component Children rendering renders function children with closeDrawer prop" duration="9"/>
        <testCase name="Drawer component Children rendering does not render children when drawer is closed" duration="4"/>
        <testCase name="Drawer component Trigger behavior clones trigger element with onClick handler" duration="6"/>
        <testCase name="Drawer component Trigger behavior handles trigger with existing props" duration="4"/>
        <testCase name="Drawer component State management toggles open state correctly" duration="14"/>
        <testCase name="Drawer component State management handles transition end event when drawer is open" duration="8"/>
        <testCase name="Drawer component Edge cases handles empty title" duration="28"/>
        <testCase name="Drawer component Edge cases handles undefined children" duration="8"/>
        <testCase name="Drawer component Edge cases handles null children" duration="8"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/RAListing/components/RAMoreFiltersDrawer.test.tsx">
        <testCase name="RAMoreFiltersDrawer renders trigger button and drawer" duration="30"/>
        <testCase name="RAMoreFiltersDrawer renders all basic filters except search" duration="11"/>
        <testCase name="RAMoreFiltersDrawer shows Add More Filters dropdown and can add optional filters" duration="201"/>
        <testCase name="RAMoreFiltersDrawer renders Clear and Apply buttons" duration="12"/>
        <testCase name="RAMoreFiltersDrawer can remove optional filters and calls onFilterChange" duration="61"/>
        <testCase name="RAMoreFiltersDrawer calls handleClearFilters and handleUpdateFilters" duration="10"/>
        <testCase name="RAMoreFiltersDrawer toggles optional filters on and off" duration="54"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/RAListing/RAListing.test.tsx">
        <testCase name="RAListing renders the main UI elements" duration="15"/>
        <testCase name="RAListing navigates to template listing on button click" duration="35"/>
        <testCase name="RAListing navigates to drafts on button click" duration="7"/>
        <testCase name="RAListing handles filter change" duration="4"/>
        <testCase name="RAListing shows table data" duration="6"/>
        <testCase name="RAListing navigates to template selection from dropdown" duration="156"/>
        <testCase name="RAListing navigates to template creation from dropdown" duration="40"/>
        <testCase name="RAListing handles create RA without template dropdown option" duration="32"/>
        <testCase name="RAListing sorts by default when sorting is cleared" duration="7"/>
        <testCase name="RAListing renders all columns in the table" duration="7"/>
        <testCase name="RAListing renders Comments and Action columns cell renderers directly for coverage" duration="12"/>
        <testCase name="RAListing renders Vessel/Office Name column with only office_name" duration="5"/>
        <testCase name="RAListing renders Vessel/Office Name column with vessel name as link" duration="5"/>
        <testCase name="RAListing renders Vessel/Office Name column with only vessel name as link" duration="4"/>
        <testCase name="RAListing renders Level of RA column with null value" duration="3"/>
        <testCase name="RAListing renders Tech Group column with string value" duration="3"/>
        <testCase name="RAListing renders Submitted on column with date" duration="2"/>
        <testCase name="RAListing renders Approval Date column with date" duration="4"/>
        <testCase name="RAListing renders Date of Risk Assessment column with date" duration="4"/>
        <testCase name="RAListing renders Comments column with null value" duration="4"/>
        <testCase name="RAListing renders Action column with id" duration="3"/>
        <testCase name="RAListing handles sorting ASC branch" duration="5"/>
        <testCase name="RAListing handles empty data gracefully" duration="3"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx">
        <testCase name="RADraftHeader Basic Rendering renders the component without crashing" duration="51"/>
        <testCase name="RADraftHeader Basic Rendering renders breadcrumb navigation correctly" duration="28"/>
        <testCase name="RADraftHeader Basic Rendering renders tab buttons correctly" duration="13"/>
        <testCase name="RADraftHeader Basic Rendering applies correct container classes" duration="3"/>
        <testCase name="RADraftHeader Basic Rendering applies correct breadcrumb container classes" duration="5"/>
        <testCase name="RADraftHeader Basic Rendering applies correct tab container classes" duration="2"/>
        <testCase name="RADraftHeader Tab Functionality shows Risk Assessment tab as active when activeTab is 1" duration="15"/>
        <testCase name="RADraftHeader Tab Functionality shows RA Template tab as active when activeTab is 2" duration="10"/>
        <testCase name="RADraftHeader Tab Functionality calls setActiveTab with 1 when Risk Assessment tab is clicked" duration="10"/>
        <testCase name="RADraftHeader Tab Functionality calls setActiveTab with 2 when RA Template tab is clicked" duration="6"/>
        <testCase name="RADraftHeader Tab Functionality handles multiple tab clicks correctly" duration="17"/>
        <testCase name="RADraftHeader Button Properties applies correct variant to tab buttons" duration="9"/>
        <testCase name="RADraftHeader Button Properties applies draft-listing-tab class to both buttons" duration="9"/>
        <testCase name="RADraftHeader Edge Cases handles activeTab value of 0 correctly" duration="12"/>
        <testCase name="RADraftHeader Edge Cases handles activeTab value greater than 2 correctly" duration="15"/>
        <testCase name="RADraftHeader Edge Cases handles negative activeTab value correctly" duration="12"/>
        <testCase name="RADraftHeader Accessibility has proper button roles" duration="21"/>
        <testCase name="RADraftHeader Accessibility has proper link role for breadcrumb" duration="9"/>
        <testCase name="RADraftHeader Component Structure maintains proper DOM hierarchy" duration="17"/>
        <testCase name="RADraftHeader Component Structure contains both tabs within tab container" duration="11"/>
        <testCase name="RADraftHeader Props Validation works with different setActiveTab functions" duration="14"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/TemplateSelection/TemplateSelection.test.tsx">
        <testCase name="TemplateSelection renders main UI and handles template selection" duration="28"/>
        <testCase name="TemplateSelection selects a template from MostlyUsedCardList and enables Use Template button" duration="17"/>
        <testCase name="TemplateSelection disables Use Template button if no template is selected" duration="6"/>
        <testCase name="TemplateSelection calls navigation on Cancel button click" duration="12"/>
        <testCase name="TemplateSelection calls navigation on ExternalLinkIcon button click" duration="7"/>
        <testCase name="TemplateSelection calls navigation on Use Template click" duration="7"/>
        <testCase name="TemplateSelection fetches more templates when Fetch More is clicked" duration="6"/>
        <testCase name="TemplateSelection resets selectedTemplate when handleFilterChange is called" duration="5"/>
        <testCase name="TemplateSelection displays correct button text" duration="3"/>
        <testCase name="TemplateSelection renders template cards from CardGallery" duration="4"/>
        <testCase name="TemplateSelection renders All Templates section" duration="5"/>
        <testCase name="TemplateSelection renders external link icon in template preview button" duration="5"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/VesselAndOfficeDropdown.test.tsx">
        <testCase name="VesselAndOfficeDropdown renders placeholder when nothing selected" duration="15"/>
        <testCase name="VesselAndOfficeDropdown renders selected vessel and office names" duration="13"/>
        <testCase name="VesselAndOfficeDropdown opens dropdown and filters options" duration="27"/>
        <testCase name="VesselAndOfficeDropdown calls onChange when vessel is selected" duration="13"/>
        <testCase name="VesselAndOfficeDropdown shows select all/clear all button" duration="14"/>
        <testCase name="VesselAndOfficeDropdown shows &quot;No options found.&quot; when search yields no results" duration="10"/>
        <testCase name="VesselAndOfficeDropdown calls onChange for select all" duration="11"/>
        <testCase name="VesselAndOfficeDropdown calls onChange for clear all" duration="8"/>
        <testCase name="VesselAndOfficeDropdown handles empty options array" duration="19"/>
        <testCase name="VesselAndOfficeDropdown closes dropdown on outside click" duration="13"/>
        <testCase name="VesselAndOfficeDropdown keyboard accessibility: Enter, Space, Escape" duration="10"/>
        <testCase name="VesselAndOfficeDropdown has proper aria attributes" duration="5"/>
        <testCase name="VesselAndOfficeDropdown shows counter and SingleBadgePopover when more than maxDisplayNames selected" duration="4"/>
        <testCase name="VesselAndOfficeDropdown handles grouped options and vessel/office selection" duration="28"/>
        <testCase name="VesselAndOfficeDropdown renders empty group labels but not No options found if groups exist" duration="20"/>
        <testCase name="VesselAndOfficeDropdown does not call onChange in handleSelectAll when no options" duration="30"/>
        <testCase name="VesselAndOfficeDropdown calls onChange with null when last vessel is deselected" duration="11"/>
        <testCase name="VesselAndOfficeDropdown calls onChange with null when last office is deselected" duration="5"/>
        <testCase name="VesselAndOfficeDropdown toggles selection in onChange (add and remove vessel)" duration="24"/>
        <testCase name="VesselAndOfficeDropdown toggles selection in onChange (add and remove office)" duration="31"/>
        <testCase name="VesselAndOfficeDropdown does not close dropdown on outside click if dropdownRef is not set" duration="5"/>
        <testCase name="VesselAndOfficeDropdown covers early return in useLayoutEffect when refs are not set" duration="17"/>
        <testCase name="VesselAndOfficeDropdown covers setMaxDisplayNames logic when namesMeasureRef has no children" duration="9"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/services/services.test.ts">
        <testCase name="Services getRiskCategoryList should fetch risk categories successfully" duration="0"/>
        <testCase name="Services getRiskCategoryList should handle API errors" duration="11"/>
        <testCase name="Services getHazardsList should fetch hazards list successfully" duration="2"/>
        <testCase name="Services getHazardsList should handle API errors" duration="0"/>
        <testCase name="Services getRiskParameterType should fetch risk parameter types successfully" duration="5"/>
        <testCase name="Services getRiskParameterType should handle API errors" duration="10"/>
        <testCase name="Services getMainRiskParameterType should fetch main risk parameter types without filter" duration="2"/>
        <testCase name="Services getMainRiskParameterType should fetch main risk parameter types with filter for risk rating" duration="1"/>
        <testCase name="Services getMainRiskParameterType should handle API errors" duration="1"/>
        <testCase name="Services getTaskReliabilityAssessList should fetch task reliability assessment list successfully" duration="0"/>
        <testCase name="Services getTaskReliabilityAssessList should handle API errors" duration="0"/>
        <testCase name="Services generateQueryParams should generate query params for single value" duration="0"/>
        <testCase name="Services generateQueryParams should generate query params for multiple values" duration="0"/>
        <testCase name="Services generateQueryParams should handle empty array" duration="1"/>
        <testCase name="Services generateQueryParams should encode special characters" duration="0"/>
        <testCase name="Services generateQueryParams should handle values with spaces and special characters" duration="0"/>
        <testCase name="Services GET_VESSEL_USER_DETAILS should generate correct URL with default parameters" duration="0"/>
        <testCase name="Services GET_VESSEL_USER_DETAILS should generate correct URL with custom key and single id" duration="0"/>
        <testCase name="Services GET_VESSEL_USER_DETAILS should generate correct URL with custom key and multiple ids" duration="0"/>
        <testCase name="Services GET_VESSEL_USER_DETAILS should handle empty ids array" duration="0"/>
        <testCase name="Services getTemplateList should fetch template list successfully with basic params" duration="1"/>
        <testCase name="Services getTemplateList should handle API errors" duration="1"/>
        <testCase name="Services getTemplateUserList should fetch template user list successfully" duration="0"/>
        <testCase name="Services getTemplateUserList should handle API errors" duration="4"/>
        <testCase name="Services Uncovered service functions markTemplateAsArchived should PATCH and return data" duration="1"/>
        <testCase name="Services Uncovered service functions getMostlyUsedTemplates should GET and return result" duration="0"/>
        <testCase name="Services Uncovered service functions getRAList should GET with params and signal" duration="1"/>
        <testCase name="Services Uncovered service functions getRAStringOptions should GET and return data" duration="0"/>
        <testCase name="Services Uncovered service functions getTemplateById should GET and return data" duration="0"/>
        <testCase name="Services Uncovered service functions deleteTemplateById should DELETE and return data" duration="0"/>
        <testCase name="Services Uncovered service functions deleteRiskById should DELETE and return data" duration="5"/>
        <testCase name="Services Uncovered service functions createNewTemplate should POST and return data" duration="1"/>
        <testCase name="Services Uncovered service functions updateSavedTemplate should PATCH and return data" duration="0"/>
        <testCase name="Services Uncovered service functions getVesselsList should GET and return data" duration="1"/>
        <testCase name="Services Uncovered service functions getOfficesList should GET and return data" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for markTemplateAsArchived" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for getMostlyUsedTemplates" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for getRAList" duration="1"/>
        <testCase name="Services Uncovered service functions should throw on error for getRAStringOptions" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for getTemplateById" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for deleteTemplateById" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for deleteRiskById" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for createNewTemplate" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for updateSavedTemplate" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for getVesselsList" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for getOfficesList" duration="2"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/ConfirmPublishDetailsModal.test.tsx">
        <testCase name="ConfirmPublishDetailsModal renders modal with initial keywords" duration="48"/>
        <testCase name="ConfirmPublishDetailsModal adds a new keyword on Enter key press" duration="12"/>
        <testCase name="ConfirmPublishDetailsModal does not add duplicate keywords" duration="19"/>
        <testCase name="ConfirmPublishDetailsModal removes keyword when X icon is clicked" duration="46"/>
        <testCase name="ConfirmPublishDetailsModal calls onSave with keywords and closes on confirm" duration="5"/>
        <testCase name="ConfirmPublishDetailsModal calls onClose without saving on cancel" duration="9"/>
        <testCase name="ConfirmPublishDetailsModal disables confirm button when no keywords" duration="6"/>
        <testCase name="ConfirmPublishDetailsModal enables confirm button when keywords are present" duration="7"/>
        <testCase name="ConfirmPublishDetailsModal adds keyword only when Enter key is pressed and input is not empty" duration="13"/>
        <testCase name="ConfirmPublishDetailsModal does not add keyword on other key presses" duration="11"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/AtRiskStep.test.tsx">
        <testCase name="AtRiskStep Component renders correctly with default props" duration="13"/>
        <testCase name="AtRiskStep Component calls generateGroupedOptions with correct parameters" duration="20"/>
        <testCase name="AtRiskStep Component passes correct props to GroupedCheckboxGrid" duration="9"/>
        <testCase name="AtRiskStep Component handles form changes correctly" duration="5"/>
        <testCase name="AtRiskStep Component validates correctly when no parameters are selected" duration="5"/>
        <testCase name="AtRiskStep Component validates correctly when parameters are selected" duration="5"/>
        <testCase name="AtRiskStep Component validates correctly when others option is selected with text" duration="8"/>
        <testCase name="AtRiskStep Component validates correctly when others option is selected but text is empty" duration="6"/>
        <testCase name="AtRiskStep Component validates correctly when others option has only whitespace" duration="3"/>
        <testCase name="AtRiskStep Component handles mixed validation scenarios correctly" duration="4"/>
        <testCase name="AtRiskStep Component handles empty parameters array correctly" duration="6"/>
        <testCase name="AtRiskStep Component handles non-array parameters correctly" duration="4"/>
        <testCase name="AtRiskStep Component calls onValidate on form parameter changes" duration="12"/>
        <testCase name="AtRiskStep Component works without onValidate callback" duration="5"/>
        <testCase name="AtRiskStep Component handles empty task_requiring_ra correctly" duration="6"/>
        <testCase name="AtRiskStep Component handles undefined task_requiring_ra correctly" duration="5"/>
        <testCase name="AtRiskStep Component handles others change correctly" duration="5"/>
        <testCase name="AtRiskStep Component exposes validate method through ref" duration="3"/>
        <testCase name="AtRiskStep Component handles complex parameter validation scenarios" duration="6"/>
        <testCase name="AtRiskStep Component handles validation with null parameter_id arrays" duration="5"/>
        <testCase name="AtRiskStep Component handles validation with undefined parameter_id arrays" duration="27"/>
        <testCase name="AtRiskStep Component handles validation when riskParameterType is empty" duration="6"/>
        <testCase name="AtRiskStep Component handles validation when riskParameterType is undefined" duration="8"/>
        <testCase name="AtRiskStep Component validates correctly with multiple parameters having mixed validity" duration="5"/>
        <testCase name="AtRiskStep Component handles form updates correctly when setForm is called multiple times" duration="9"/>
        <testCase name="AtRiskStep Component maintains component stability when props change" duration="6"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/SearchDropdown.test.tsx">
        <testCase name="SearchDropdown renders placeholder when nothing selected" duration="10"/>
        <testCase name="SearchDropdown renders selected values" duration="5"/>
        <testCase name="SearchDropdown opens dropdown and filters options" duration="49"/>
        <testCase name="SearchDropdown calls onChange when option is selected" duration="13"/>
        <testCase name="SearchDropdown shows select all/clear all button" duration="7"/>
        <testCase name="SearchDropdown shows &quot;No options found.&quot; when search yields no results" duration="6"/>
        <testCase name="SearchDropdown calls onChange for select all" duration="9"/>
        <testCase name="SearchDropdown calls onChange for clear all" duration="9"/>
        <testCase name="SearchDropdown handles empty options array" duration="10"/>
        <testCase name="SearchDropdown closes dropdown on outside click" duration="8"/>
        <testCase name="SearchDropdown keyboard accessibility: Enter, Space, Escape" duration="11"/>
        <testCase name="SearchDropdown has proper aria attributes" duration="4"/>
        <testCase name="SearchDropdown shows counter and SingleBadgePopover when more than maxDisplayNames selected" duration="6"/>
        <testCase name="SearchDropdown does not crash if refs are not set in useLayoutEffect" duration="5"/>
        <testCase name="SearchDropdown does not call onChange for select all/clear all when no options" duration="16"/>
        <testCase name="SearchDropdown calls onChange when CheckboxComponent onChange is triggered" duration="7"/>
        <testCase name="SearchDropdown does not close dropdown on outside click if dropdownRef is not set" duration="8"/>
        <testCase name="SearchDropdown handles setMaxDisplayNames when namesMeasureRef has no children" duration="5"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/CheckboxComponent.test.tsx">
        <testCase name="CheckboxComponent Rendering renders label correctly" duration="11"/>
        <testCase name="CheckboxComponent Rendering renders CheckFilled icon when checked is true" duration="7"/>
        <testCase name="CheckboxComponent Rendering renders CheckUnFilled icon when checked is false" duration="5"/>
        <testCase name="CheckboxComponent Rendering renders without label when label is not provided" duration="6"/>
        <testCase name="CheckboxComponent Rendering renders with React node as label" duration="4"/>
        <testCase name="CheckboxComponent Rendering has correct CSS classes and structure" duration="6"/>
        <testCase name="CheckboxComponent Rendering contains hidden input element with correct attributes" duration="5"/>
        <testCase name="CheckboxComponent Rendering hidden input reflects unchecked state" duration="4"/>
        <testCase name="CheckboxComponent Click Interactions calls onChange when label is clicked" duration="8"/>
        <testCase name="CheckboxComponent Click Interactions stops propagation on label click" duration="6"/>
        <testCase name="CheckboxComponent Click Interactions stops propagation on hidden input click" duration="4"/>
        <testCase name="CheckboxComponent Keyboard Interactions calls onChange when Enter key is pressed on hidden input" duration="4"/>
        <testCase name="CheckboxComponent Keyboard Interactions does not call onChange when other keys are pressed" duration="5"/>
        <testCase name="CheckboxComponent Keyboard Interactions stops propagation on input change events" duration="5"/>
        <testCase name="CheckboxComponent Accessibility has correct accessibility attributes when checked" duration="4"/>
        <testCase name="CheckboxComponent Accessibility has correct accessibility attributes when unchecked" duration="6"/>
        <testCase name="CheckboxComponent Accessibility has correct label association" duration="3"/>
        <testCase name="CheckboxComponent Accessibility label is properly associated with input" duration="3"/>
        <testCase name="CheckboxComponent Accessibility works without label" duration="5"/>
        <testCase name="CheckboxComponent Accessibility handles React node labels" duration="4"/>
        <testCase name="CheckboxComponent Accessibility is focusable and accessible" duration="5"/>
        <testCase name="CheckboxComponent Edge Cases handles multiple rapid clicks correctly" duration="6"/>
        <testCase name="CheckboxComponent Edge Cases handles multiple click events" duration="6"/>
        <testCase name="CheckboxComponent Edge Cases maintains visual consistency with label styling" duration="5"/>
        <testCase name="CheckboxComponent Edge Cases handles empty string label" duration="5"/>
        <testCase name="CheckboxComponent Edge Cases handles special characters in id" duration="3"/>
        <testCase name="CheckboxComponent State Changes updates icon when checked state changes" duration="9"/>
        <testCase name="CheckboxComponent State Changes updates checked state when checked prop changes" duration="5"/>
        <testCase name="CheckboxComponent State Changes updates hidden input checked state" duration="7"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/hooks/useQuery.test.tsx">
        <testCase name="useQuery Basic functionality should fetch data successfully and show loading states" duration="20"/>
        <testCase name="useQuery Basic functionality should not fetch when enabled is false" duration="6"/>
        <testCase name="useQuery Basic functionality should refetch data when refetch is called" duration="13"/>
        <testCase name="useQuery Query key handling should handle string query keys" duration="7"/>
        <testCase name="useQuery Query key handling should handle array query keys" duration="6"/>
        <testCase name="useQuery Query key handling should refetch when query key changes" duration="10"/>
        <testCase name="useQuery Options handling should respect staleTime option" duration="13"/>
        <testCase name="useQuery Options handling should refetch in background when staleTime is 0" duration="11"/>
        <testCase name="useQuery Options handling should handle cache cleanup with cacheTime" duration="14"/>
        <testCase name="useQuery Error handling and edge cases should handle component unmounting during fetch and prevent state updates" duration="3"/>
        <testCase name="useQuery Error handling and edge cases should handle query function that returns null" duration="4"/>
        <testCase name="useQuery Error handling and edge cases should handle complex query keys with objects" duration="8"/>
        <testCase name="useQuery State transitions should transition from idle to loading to success" duration="9"/>
        <testCase name="useQuery State transitions should transition from loading to error on failure" duration="26"/>
        <testCase name="useQuery State transitions should reset error state on successful refetch" duration="34"/>
        <testCase name="useQuery Helper functions should provide correct boolean flags" duration="7"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/CustomDatePickerWithRange.test.tsx">
        <testCase name="CustomDatePickerWithRange renders with label and placeholder" duration="13"/>
        <testCase name="CustomDatePickerWithRange displays clear icon when start or end date exists" duration="11"/>
        <testCase name="CustomDatePickerWithRange clears the date range when clear icon is clicked" duration="9"/>
        <testCase name="CustomDatePickerWithRange does not show clear icon when no dates selected" duration="15"/>
        <testCase name="CustomDatePickerWithRange applies aria-label for accessibility" duration="6"/>
        <testCase name="CustomDatePickerWithRange handles date selection and formats dates correctly" duration="13"/>
        <testCase name="CustomDatePickerWithRange handles null date values correctly" duration="7"/>
        <testCase name="CustomDatePickerWithRange prevents keyboard input" duration="10"/>
        <testCase name="CustomDatePickerWithRange respects min and max date constraints" duration="9"/>
        <testCase name="CustomDatePickerWithRange clear button has appropriate accessibility label" duration="4"/>
        <testCase name="CustomDatePickerWithRange calendar icon has appropriate accessibility label" duration="6"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/HazardCategoryStep.test.tsx">
        <testCase name="HazardCategoryStep Component renders correctly with default props" duration="19"/>
        <testCase name="HazardCategoryStep Component passes correct hazards list from data store" duration="8"/>
        <testCase name="HazardCategoryStep Component handles empty hazards list gracefully" duration="23"/>
        <testCase name="HazardCategoryStep Component passes initial checked hazards correctly" duration="7"/>
        <testCase name="HazardCategoryStep Component passes others selection state correctly" duration="10"/>
        <testCase name="HazardCategoryStep Component handles hazard selection changes correctly" duration="4"/>
        <testCase name="HazardCategoryStep Component handles others selection changes correctly" duration="3"/>
        <testCase name="HazardCategoryStep Component clears others text when others is deselected" duration="5"/>
        <testCase name="HazardCategoryStep Component validates correctly when no hazards are selected" duration="5"/>
        <testCase name="HazardCategoryStep Component validates correctly when hazards are selected" duration="5"/>
        <testCase name="HazardCategoryStep Component validates correctly when others is selected with text" duration="5"/>
        <testCase name="HazardCategoryStep Component validates correctly when others is selected but text is empty" duration="3"/>
        <testCase name="HazardCategoryStep Component validates correctly when others has only whitespace" duration="6"/>
        <testCase name="HazardCategoryStep Component validates correctly when both hazards and others are selected" duration="3"/>
        <testCase name="HazardCategoryStep Component calls onValidate on form changes" duration="6"/>
        <testCase name="HazardCategoryStep Component works without onValidate callback" duration="5"/>
        <testCase name="HazardCategoryStep Component handles undefined template_hazard gracefully" duration="2"/>
        <testCase name="HazardCategoryStep Component handles missing task_requiring_ra gracefully" duration="2"/>
        <testCase name="HazardCategoryStep Component exposes validate method through ref" duration="3"/>
        <testCase name="HazardCategoryStep Component validates on component mount" duration="10"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/BadgeListPopover.test.tsx">
        <testCase name="BadgeListPopover renders the container with correct class" duration="13"/>
        <testCase name="BadgeListPopover renders individual badges with correct class and content when space allows" duration="21"/>
        <testCase name="BadgeListPopover handles empty badges array" duration="10"/>
        <testCase name="BadgeListPopover shows &quot;+X more&quot; badge when there are remaining badges" duration="21"/>
        <testCase name="BadgeListPopover popover shows remaining badges when hovering over &quot;+X more&quot;" duration="107"/>
        <testCase name="BadgeListPopover handles ResizeObserver lifecycle correctly" duration="3"/>
        <testCase name="BadgeListPopover component renders without crashing with various badge counts" duration="6"/>
        <testCase name="BadgeListPopover handles badges with special characters correctly" duration="3"/>
        <testCase name="BadgeListPopover handles very long badge names without breaking" duration="4"/>
        <testCase name="BadgeListPopover maintains component structure with different badge configurations" duration="5"/>
        <testCase name="BadgeListPopover handles container ref correctly" duration="4"/>
        <testCase name="BadgeListPopover renders without errors when no badges are provided" duration="7"/>
        <testCase name="BadgeListPopover popover contains correct content when triggered" duration="61"/>
        <testCase name="BadgeListPopover handles badge key generation correctly" duration="26"/>
        <testCase name="BadgeListPopover responds to ResizeObserver changes" duration="4"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/components/TemplateListingFilters.test.tsx">
        <testCase name="TemplateListingFilters renders all filter controls" duration="74"/>
        <testCase name="TemplateListingFilters calls onFilterChange for search input" duration="19"/>
        <testCase name="TemplateListingFilters calls onFilterChange for user dropdown" duration="8"/>
        <testCase name="TemplateListingFilters calls onFilterChange for date picker" duration="4"/>
        <testCase name="TemplateListingFilters calls onFilterChange for dropdown typeahead" duration="5"/>
        <testCase name="TemplateListingFilters handles error in useEffect" duration="65"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/SelectableCheckboxGrid.test.tsx">
        <testCase name="SelectableCheckboxGrid renders title, subtitle, and checkboxes" duration="31"/>
        <testCase name="SelectableCheckboxGrid calls onChange when a checkbox is selected or unselected" duration="10"/>
        <testCase name="SelectableCheckboxGrid filters unchecked options based on search input" duration="11"/>
        <testCase name="SelectableCheckboxGrid renders and handles &quot;Others&quot; checkbox and input when selected" duration="70"/>
        <testCase name="SelectableCheckboxGrid renders &quot;Others&quot; checkbox when not selected" duration="24"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/CreateRA/RaCategoryStep.test.tsx">
        <testCase name="RaCategoryStep Component renders correctly with default props" duration="34"/>
        <testCase name="RaCategoryStep Component passes correct risk category list from data store" duration="11"/>
        <testCase name="RaCategoryStep Component handles empty risk category list gracefully" duration="7"/>
        <testCase name="RaCategoryStep Component handles null risk category list gracefully" duration="16"/>
        <testCase name="RaCategoryStep Component passes initial checked categories correctly" duration="8"/>
        <testCase name="RaCategoryStep Component passes others selection state correctly" duration="6"/>
        <testCase name="RaCategoryStep Component handles category selection changes correctly" duration="4"/>
        <testCase name="RaCategoryStep Component handles others selection changes correctly" duration="6"/>
        <testCase name="RaCategoryStep Component clears others text when others is deselected" duration="4"/>
        <testCase name="RaCategoryStep Component validates correctly when no categories are selected" duration="6"/>
        <testCase name="RaCategoryStep Component validates correctly when categories are selected" duration="10"/>
        <testCase name="RaCategoryStep Component validates correctly when others is selected with text" duration="6"/>
        <testCase name="RaCategoryStep Component validates correctly when others is selected but text is empty" duration="20"/>
        <testCase name="RaCategoryStep Component validates correctly when others has only whitespace" duration="7"/>
        <testCase name="RaCategoryStep Component validates correctly when both categories and others are selected" duration="3"/>
        <testCase name="RaCategoryStep Component calls onValidate on form changes" duration="13"/>
        <testCase name="RaCategoryStep Component works without onValidate callback" duration="3"/>
        <testCase name="RaCategoryStep Component handles undefined template_category gracefully" duration="4"/>
        <testCase name="RaCategoryStep Component handles empty task_requiring_ra gracefully" duration="5"/>
        <testCase name="RaCategoryStep Component validates on component mount" duration="4"/>
        <testCase name="RaCategoryStep Component re-validates when form.template_category.category_id changes" duration="11"/>
        <testCase name="RaCategoryStep Component re-validates when form.template_category changes" duration="21"/>
        <testCase name="RaCategoryStep Component does not re-validate when other form properties change" duration="6"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/CustomDatePicker.test.tsx">
        <testCase name="CustomDatePicker renders with label and placeholder" duration="10"/>
        <testCase name="CustomDatePicker renders clear icon when value is present and clears on click" duration="11"/>
        <testCase name="CustomDatePicker shows calendar icon when no value and not required" duration="12"/>
        <testCase name="CustomDatePicker displays default error message when required and errorMsg not provided" duration="12"/>
        <testCase name="CustomDatePicker applies aria-label and correct id" duration="5"/>
        <testCase name="CustomDatePicker displays custom error message when provided" duration="4"/>
        <testCase name="CustomDatePicker prevents keyboard input" duration="4"/>
        <testCase name="CustomDatePicker renders with min and max date constraints" duration="3"/>
        <testCase name="CustomDatePicker adds invalid class when required and no value" duration="2"/>
        <testCase name="CustomDatePicker handles date selection" duration="5"/>
        <testCase name="CustomDatePicker calendar icon is not visible when value is present" duration="4"/>
        <testCase name="CustomDatePicker clear button is accessible" duration="6"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/GroupedCheckboxGrid.test.tsx">
        <testCase name="GroupedCheckboxGrid renders title, subtitle and group labels" duration="81"/>
        <testCase name="GroupedCheckboxGrid calls onChange when a checkbox is selected" duration="21"/>
        <testCase name="GroupedCheckboxGrid toggles &quot;Others&quot; checkbox and inputs text" duration="23"/>
        <testCase name="GroupedCheckboxGrid trims &quot;Others&quot; input to max length" duration="23"/>
        <testCase name="GroupedCheckboxGrid removes &quot;Others&quot; text when unchecked" duration="18"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/Stepper.test.tsx">
        <testCase name="Stepper Component renders the correct number of steps" duration="12"/>
        <testCase name="Stepper Component displays the correct labels for each step" duration="6"/>
        <testCase name="Stepper Component highlights the current active step" duration="34"/>
        <testCase name="Stepper Component shows checkmark for completed steps" duration="6"/>
        <testCase name="Stepper Component shows empty circle for incomplete steps" duration="5"/>
        <testCase name="Stepper Component allows clicking on completed steps" duration="7"/>
        <testCase name="Stepper Component does not allow clicking on current or future steps" duration="7"/>
        <testCase name="Stepper Component applies correct cursor style based on step state" duration="9"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/RAListing/components/RAFilters.test.tsx">
        <testCase name="RAFilters renders all filter controls and more filters drawer" duration="42"/>
        <testCase name="RAFilters calls onFilterChange for each filter type" duration="14"/>
        <testCase name="RAFilters calls onFilterChange for CustomDatePickerWithRange" duration="26"/>
        <testCase name="RAFilters calls onFilterChange for VesselAndOfficeDropdown" duration="14"/>
        <testCase name="RAFilters calls onFilterChange for SearchDropdown" duration="11"/>
        <testCase name="RAFilters getRaBasicFiltersFormConfig returns all filter configs" duration="0"/>
        <testCase name="RAFilters handles async error in useEffect and shows toast" duration="31"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/BottomButton.test.tsx">
        <testCase name="BottomButton renders the correct number of buttons" duration="13"/>
        <testCase name="BottomButton renders button titles correctly" duration="3"/>
        <testCase name="BottomButton calls onClick handler when button is clicked" duration="8"/>
        <testCase name="BottomButton does not call onClick handler when disabled button is clicked" duration="4"/>
        <testCase name="BottomButton applies custom class correctly" duration="6"/>
        <testCase name="BottomButton applies the correct button type and variant" duration="3"/>
        <testCase name="BottomButton applies disabled attribute when specified" duration="3"/>
        <testCase name="BottomButton uses &quot;primary&quot; as default variant when none is provided" duration="5"/>
        <testCase name="BottomButton uses &quot;button&quot; as default type when none is provided" duration="4"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/hooks/useContextWrapper.test.tsx">
        <testCase name="useContextWrapper returns context value when inside provider" duration="7"/>
        <testCase name="useContextWrapper throws error when used outside provider" duration="9"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/services/http-service.test.ts">
        <testCase name="httpService Basic exports should export HttpMethods with correct values" duration="0"/>
        <testCase name="httpService Basic exports should return an axios instance from getAxiosClient" duration="0"/>
        <testCase name="httpService Basic exports should export the axios module" duration="0"/>
        <testCase name="httpService Basic exports should export cancelPreviousRequest function" duration="0"/>
        <testCase name="httpService Axios instance configuration should create axios instance with interceptors" duration="0"/>
        <testCase name="httpService Axios instance configuration should have interceptor methods available" duration="0"/>
        <testCase name="httpService Axios instance configuration should return same instance on multiple calls" duration="0"/>
        <testCase name="httpService Error handling utilities should identify cancellation errors correctly" duration="1"/>
        <testCase name="httpService Error handling utilities should identify non-cancellation errors correctly" duration="0"/>
        <testCase name="httpService cancelPreviousRequest should create new controller for first request to endpoint" duration="0"/>
        <testCase name="httpService cancelPreviousRequest should cancel previous request and create new controller for same endpoint" duration="0"/>
        <testCase name="httpService cancelPreviousRequest should handle multiple different endpoints independently" duration="1"/>
        <testCase name="httpService cancelPreviousRequest should only cancel controller for specific endpoint" duration="0"/>
        <testCase name="httpService cancelPreviousRequest should handle empty endpoint string" duration="0"/>
        <testCase name="httpService cancelPreviousRequest should handle special characters in endpoint names" duration="1"/>
        <testCase name="httpService Integration Tests should maintain separate axios instance from global axios" duration="0"/>
        <testCase name="httpService Integration Tests should have all required methods on axios instance" duration="0"/>
        <testCase name="httpService Module structure should export all required properties" duration="0"/>
        <testCase name="httpService Module structure should have HttpMethods as an object with string values" duration="0"/>
        <testCase name="httpService Module structure should have getAxiosClient as a function" duration="1"/>
        <testCase name="httpService Module structure should have cancelPreviousRequest as a function" duration="0"/>
        <testCase name="httpService Interceptor functionality verification should have interceptors configured on axios instance" duration="0"/>
        <testCase name="httpService Interceptor functionality verification should handle axios isCancel method correctly" duration="0"/>
        <testCase name="httpService Edge cases and error scenarios should handle multiple calls to getAxiosClient" duration="0"/>
        <testCase name="httpService Edge cases and error scenarios should handle cancelPreviousRequest with various endpoint formats" duration="8"/>
        <testCase name="httpService Edge cases and error scenarios should maintain separate controllers for different endpoints" duration="0"/>
        <testCase name="httpService httpService uncovered/error branches request interceptor adds Authorization if token present" duration="1"/>
        <testCase name="httpService httpService uncovered/error branches request interceptor leaves headers if no token" duration="1"/>
        <testCase name="httpService httpService uncovered/error branches request interceptor passes through error" duration="0"/>
        <testCase name="httpService httpService uncovered/error branches response interceptor returns response" duration="1"/>
        <testCase name="httpService httpService uncovered/error branches response interceptor handles axios.isCancel" duration="2"/>
        <testCase name="httpService httpService uncovered/error branches response interceptor passes through non-cancel errors" duration="1"/>
        <testCase name="httpService httpService uncovered/error branches cancelPreviousRequest aborts and replaces controllers" duration="1"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/UnitSelectInputComponent.test.tsx">
        <testCase name="UnitSelectInputComponent renders label and input with default value" duration="16"/>
        <testCase name="UnitSelectInputComponent renders unit options in select dropdown" duration="4"/>
        <testCase name="UnitSelectInputComponent calls onChange when input value changes" duration="10"/>
        <testCase name="UnitSelectInputComponent calls onChange when select value changes" duration="3"/>
        <testCase name="UnitSelectInputComponent displays error message if error prop is passed" duration="6"/>
        <testCase name="UnitSelectInputComponent displays help text and max length counter" duration="3"/>
        <testCase name="UnitSelectInputComponent respects disabled prop on input" duration="5"/>
        <testCase name="UnitSelectInputComponent renders with custom placeholder" duration="7"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/utils/svgIcons.test.tsx">
        <testCase name="SVG Icons SortIcon renders correctly" duration="5"/>
        <testCase name="SVG Icons CheckFilled renders correctly" duration="7"/>
        <testCase name="SVG Icons CheckUnFilled renders correctly" duration="3"/>
        <testCase name="SVG Icons CommentIcon renders correctly" duration="4"/>
        <testCase name="SVG Icons InfoIcon renders correctly" duration="2"/>
        <testCase name="SVG Icons CalendarIcon renders correctly" duration="3"/>
        <testCase name="SVG Icons DeleteJobIcon renders correctly" duration="3"/>
        <testCase name="SVG Icons JobCardArrowUpIcon renders correctly" duration="4"/>
        <testCase name="SVG Icons JobCardArrowDownIcon renders correctly" duration="2"/>
        <testCase name="SVG Icons EditFormDetailIcon renders correctly" duration="4"/>
        <testCase name="SVG Icons ActionMenuIcon renders correctly" duration="4"/>
        <testCase name="SVG Icons RadioUncheckedIcon renders correctly" duration="4"/>
        <testCase name="SVG Icons RadioCheckedIcon renders correctly" duration="4"/>
        <testCase name="SVG Icons Icons accept and pass through props" duration="3"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/TemplateListing.test.tsx">
        <testCase name="TemplateListing renders header, filters, mostly used card, and table" duration="14"/>
        <testCase name="TemplateListing renders table with correct sorting callback" duration="4"/>
        <testCase name="getColumns renders Task Required cell" duration="1"/>
        <testCase name="getColumns renders No. of Risk Categories cell with values" duration="0"/>
        <testCase name="getColumns renders No. of Risk Categories cell with empty array" duration="0"/>
        <testCase name="getColumns renders No. of Hazard Categories cell with values" duration="1"/>
        <testCase name="getColumns renders No. of Hazard Categories cell with empty array" duration="0"/>
        <testCase name="getColumns renders Created on cell" duration="2"/>
        <testCase name="getColumns renders Created by cell with user" duration="1"/>
        <testCase name="getColumns renders Created by cell with missing user" duration="0"/>
        <testCase name="getColumns renders Keywords cell with values" duration="3"/>
        <testCase name="getColumns renders Keywords cell with empty array" duration="0"/>
        <testCase name="getColumns renders Action cell" duration="5"/>
        <testCase name="getColumns renders Action cell with missing user" duration="4"/>
        <testCase name="TemplateListing integration calls sorting callback and resets sorting" duration="3"/>
        <testCase name="TemplateListing integration calls handleFilterChange" duration="4"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/CategoriesFiltersDrawer.test.tsx">
        <testCase name="CategoriesFiltersDrawer renders trigger and drawer content" duration="16"/>
        <testCase name="CategoriesFiltersDrawer shows filter counts in label" duration="8"/>
        <testCase name="CategoriesFiltersDrawer calls onFilterChange when clear is clicked" duration="8"/>
        <testCase name="CategoriesFiltersDrawer calls onFilterChange when apply is clicked" duration="7"/>
        <testCase name="CategoriesFiltersDrawer renders risk and hazard checkboxes" duration="6"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/icons/external-link-icon.test.tsx">
        <testCase name="ExternalLinkIcon renders with default props" duration="9"/>
        <testCase name="ExternalLinkIcon renders with custom size and color" duration="3"/>
        <testCase name="ExternalLinkIcon forwards extra props" duration="3"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/GenericStepper.test.tsx">
        <testCase name="GenericStepper renders first step content and breadcrumb title" duration="35"/>
        <testCase name="GenericStepper calls onClose when close icon is clicked" duration="33"/>
        <testCase name="GenericStepper navigates to next step on Next click and calls onNext" duration="14"/>
        <testCase name="GenericStepper calls primaryBtnOnClick on final step" duration="12"/>
        <testCase name="GenericStepper calls secondaryBtnOnClick when Cancel is clicked" duration="5"/>
        <testCase name="GenericStepper disables buttons when disabled props are passed" duration="7"/>
        <testCase name="GenericStepper supports function-based primary button title" duration="11"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/TemplateSelection/components/TemplateSelectionFilters.test.tsx">
        <testCase name="TemplateSelectionFilter renders search input and categories drawer" duration="10"/>
        <testCase name="TemplateSelectionFilter calls onFilterChange for search" duration="4"/>
        <testCase name="TemplateSelectionFilter calls onFilterChange for RA and Hazard categories" duration="3"/>
        <testCase name="TemplateSelectionFilter shows toast on data load error" duration="54"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/SearchInput.test.tsx">
        <testCase name="SearchInput renders with custom placeholder" duration="21"/>
        <testCase name="SearchInput calls onSearch with null when input is empty or whitespace only" duration="11"/>
        <testCase name="SearchInput calls onSearch with cleaned value (removes % and _)" duration="9"/>
        <testCase name="SearchInput passes disabled prop to input" duration="6"/>
        <testCase name="SearchInput reflects the controlled value prop" duration="11"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/hooks/useInfiniteQuery.test.tsx">
        <testCase name="useInfiniteQuery fetches initial data and shows loading state" duration="19"/>
        <testCase name="useInfiniteQuery fetches next page and appends data" duration="20"/>
        <testCase name="useInfiniteQuery handles errors on fetch" duration="12"/>
        <testCase name="useInfiniteQuery refetches data when refetch is called" duration="21"/>
        <testCase name="useInfiniteQuery resets to initial page and refetches data when reset is called" duration="20"/>
        <testCase name="useInfiniteQuery handles no next page from the start" duration="4"/>
        <testCase name="useInfiniteQuery prevents fetching next page if already fetching" duration="24"/>
        <testCase name="useInfiniteQuery prevents fetching next page if hasNextPage is false" duration="6"/>
        <testCase name="useInfiniteQuery resets and refetches when options change after mount" duration="13"/>
        <testCase name="useInfiniteQuery handles empty response data gracefully" duration="10"/>
        <testCase name="useInfiniteQuery resets state and data correctly" duration="9"/>
        <testCase name="useInfiniteQuery does not fetch next page when hasNextPage is false" duration="17"/>
        <testCase name="useInfiniteQuery does not fetch next page if already fetching" duration="5"/>
        <testCase name="useInfiniteQuery uses default page and limit values when options are not provided" duration="24"/>
        <testCase name="useInfiniteQuery handles fetch error and sets error state" duration="20"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/Link.test.tsx">
        <testCase name="Link renders children and href" duration="54"/>
        <testCase name="Link calls navigate on click" duration="6"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/root.component.test.tsx">
        <testCase name="Root Component renders loading spinner while initializing user service" duration="12"/>
        <testCase name="Root Component renders loading spinner while roleConfig is null" duration="6"/>
        <testCase name="Root Component renders main app structure after loading" duration="5"/>
        <testCase name="Root Component calls ga4EventTrigger and handles errors" duration="9"/>
        <testCase name="Root Component renders loading spinner with correct accessibility attributes" duration="9"/>
        <testCase name="Root Component does not throw if ga4react is missing" duration="3"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/FormCheckRadio.test.tsx">
        <testCase name="FormCheckRadio renders unchecked radio button" duration="10"/>
        <testCase name="FormCheckRadio renders checked radio button" duration="3"/>
        <testCase name="FormCheckRadio renders the label when provided" duration="2"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/icons/close-icon.test.tsx">
        <testCase name="CloseIcon renders without crashing" duration="5"/>
        <testCase name="CloseIcon applies passed props correctly" duration="1"/>
        <testCase name="CloseIcon has correct width, height and viewBox attributes" duration="3"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/CardGallery.test.tsx">
        <testCase name="CardGallery renders loading state" duration="5"/>
        <testCase name="CardGallery renders empty state" duration="1"/>
        <testCase name="CardGallery renders items" duration="3"/>
        <testCase name="CardGallery calls fetchNextPage on scroll near bottom" duration="1"/>
        <testCase name="CardGallery shows spinner when fetching next page" duration="3"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/utils/common.test.ts">
        <testCase name="cleanObject removes keys with null or undefined values" duration="0"/>
        <testCase name="cleanObject removes keys with empty arrays" duration="0"/>
        <testCase name="cleanObject removes nested empty objects recursively" duration="1"/>
        <testCase name="cleanObject keeps nested objects with non-empty keys" duration="0"/>
        <testCase name="cleanObject returns empty object if all values are empty or null" duration="0"/>
        <testCase name="parseDate returns formatted date string for valid date input" duration="2"/>
        <testCase name="parseDate returns undefined for invalid date string" duration="13"/>
        <testCase name="parseDate returns undefined when date is null or undefined" duration="0"/>
        <testCase name="parseDate formats date with custom format string" duration="1"/>
        <testCase name="getDateRangeFilters returns empty object if no range is provided" duration="0"/>
        <testCase name="getDateRangeFilters returns only start_date filter if only start date is provided" duration="0"/>
        <testCase name="getDateRangeFilters returns only end_date filter if only end date is provided" duration="0"/>
        <testCase name="getDateRangeFilters returns both start_date and end_date filters if both provided" duration="0"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/paris2-risk-assessment.test.tsx">
        <testCase name="paris2-risk-assessment lifecycles should export bootstrap, mount, and unmount lifecycle functions" duration="0"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/InputComponent.test.tsx">
        <testCase name="InputComponent renders label and input with correct props" duration="10"/>
        <testCase name="InputComponent renders a textarea when type is textarea" duration="5"/>
        <testCase name="InputComponent calls onChange and onBlur handlers" duration="4"/>
        <testCase name="InputComponent displays helpText and error messages correctly" duration="3"/>
        <testCase name="InputComponent applies disabled attribute" duration="2"/>
        <testCase name="InputComponent renders max length counter when showMaxLength is true" duration="4"/>
        <testCase name="InputComponent applies custom className and style" duration="7"/>
        <testCase name="InputComponent uses custom classes from classes prop" duration="7"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/utils/user.test.ts">
        <testCase name="getInitials returns empty string for undefined" duration="1"/>
        <testCase name="getInitials returns empty string for empty string" duration="0"/>
        <testCase name="getInitials returns first two letters uppercased for single word" duration="0"/>
        <testCase name="getInitials returns initials for two words" duration="0"/>
        <testCase name="getInitials returns initials for more than two words" duration="0"/>
        <testCase name="getInitials handles extra spaces" duration="0"/>
        <testCase name="getInitials handles names with non-letters" duration="1"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/rootRoutes.test.tsx">
        <testCase name="AppRoutes renders routes with components" duration="12"/>
        <testCase name="AppRoutes renders redirect route" duration="12"/>
        <testCase name="AppRoutes redirects when isPermission is false" duration="7"/>
        <testCase name="AppRoutes renders Not Found for unknown routes" duration="4"/>
        <testCase name="AppRoutes shows fallback while loading" duration="8"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/SingleBadgePopover.test.tsx">
        <testCase name="SingleBadgePopover renders the badge label" duration="8"/>
        <testCase name="SingleBadgePopover shows popover with items on hover" duration="38"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/icons/trash-icon.test.tsx">
        <testCase name="TrashIcon renders with default props" duration="9"/>
        <testCase name="TrashIcon renders with custom width, height, and color" duration="4"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/TruncateBasicText.test.tsx">
        <testCase name="TruncateText renders full text when text length is within maxLength" duration="9"/>
        <testCase name="TruncateText renders truncated text when text length exceeds maxLength" duration="5"/>
        <testCase name="TruncateText does not show popover if text is not truncated" duration="16"/>
        <testCase name="TruncateText shows full text in popover on hover when text is truncated" duration="40"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/icons/plus-icon.test.tsx">
        <testCase name="PlusIcon renders with default size and color" duration="11"/>
        <testCase name="PlusIcon applies custom width and height" duration="6"/>
        <testCase name="PlusIcon applies custom color" duration="1"/>
        <testCase name="PlusIcon applies custom className and style" duration="13"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/components/TemplateListingHeader.test.tsx">
        <testCase name="TemplateListingHeader renders breadcrumb and create new button" duration="6"/>
        <testCase name="TemplateListingHeader does not render create new button if permission is false" duration="4"/>
        <testCase name="TemplateListingHeader navigates to /risk-assessment when breadcrumb button is clicked" duration="4"/>
        <testCase name="TemplateListingHeader navigates to /risk-assessment/templates/create when create new is clicked" duration="4"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/ProjectBreadCrumb.test.tsx">
        <testCase name="ProjectBreadCrumb renders breadcrumb without links when none are provided" duration="12"/>
        <testCase name="ProjectBreadCrumb applies correct text and link styles" duration="5"/>
        <testCase name="ProjectBreadCrumb preserves state in the link if provided" duration="3"/>
        <testCase name="ProjectBreadCrumb renders correctly with only one item" duration="3"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/utils/error.test.ts">
        <testCase name="extractErrorMessage returns default message when error is falsy or has no message" duration="0"/>
        <testCase name="extractErrorMessage returns error.message if present" duration="0"/>
        <testCase name="extractErrorMessage returns axios error message if isAxiosError is true and message exists" duration="0"/>
        <testCase name="extractErrorMessage returns nested axios response.data.message if present" duration="0"/>
        <testCase name="extractErrorMessage falls back to axios error message if response.data.message is missing" duration="0"/>
        <testCase name="extractErrorMessage handles error that is actually an AxiosError instance" duration="0"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/controller/user-role-controller.test.ts">
        <testCase name="UserRoleController getConfig Basic functionality should return config with user and riskAssessment properties" duration="0"/>
        <testCase name="UserRoleController getConfig Basic functionality should return user from tokenParsed" duration="1"/>
        <testCase name="UserRoleController getConfig Role-based permissions canCreateNewTemplate permission should return true when user has RA_CREATE_TEMPLATE_ACCESS role" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions canCreateNewTemplate permission should return false when user does not have RA_CREATE_TEMPLATE_ACCESS role" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions canCreateNewTemplate permission should return false when user has no roles" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions hasPermision (view access) should return true when user has RA_VIEW_ACCESS role" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions hasPermision (view access) should return false when user does not have RA_VIEW_ACCESS role" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions hasPermision (view access) should return false when user has no roles" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions Multiple roles scenarios should handle user with both create and view permissions" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions Multiple roles scenarios should handle user with only view permission" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions Multiple roles scenarios should handle user with only create permission" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions Multiple roles scenarios should handle user with multiple unrelated roles" duration="0"/>
        <testCase name="UserRoleController getConfig Edge cases and error handling should handle missing realmAccess" duration="7"/>
        <testCase name="UserRoleController getConfig Edge cases and error handling should handle missing realmAccess.roles" duration="1"/>
        <testCase name="UserRoleController getConfig Edge cases and error handling should handle null/undefined roles array" duration="0"/>
        <testCase name="UserRoleController getConfig Edge cases and error handling should handle empty tokenParsed" duration="0"/>
        <testCase name="UserRoleController getConfig Edge cases and error handling should handle null tokenParsed" duration="0"/>
        <testCase name="UserRoleController getConfig Role constants validation should use correct role constants" duration="0"/>
        <testCase name="UserRoleController getConfig Role constants validation should work with actual role constant values" duration="0"/>
        <testCase name="UserRoleController getConfig Return type validation should return object with correct structure" duration="1"/>
        <testCase name="UserRoleController getConfig Return type validation should return consistent results for same input" duration="0"/>
        <testCase name="UserRoleController getConfig Integration scenarios should work with realistic user data" duration="0"/>
        <testCase name="UserRoleController getConfig Integration scenarios should handle case-sensitive role matching" duration="0"/>
        <testCase name="UserRoleController Class instantiation should create instance successfully" duration="0"/>
        <testCase name="UserRoleController Class instantiation should create multiple independent instances" duration="0"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/context/AlertContextProvider.test.tsx">
        <testCase name="AlertContextProvider renders children correctly" duration="7"/>
        <testCase name="AlertContextProvider shows an alert when alert() is called" duration="5"/>
        <testCase name="AlertContextProvider alertDetails is null initially" duration="3"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/components/icons/three-dot-menu-icon.test.tsx">
        <testCase name="ThreeDotsMenu renders with default size and color" duration="4"/>
        <testCase name="ThreeDotsMenu applies custom width, height, and color" duration="3"/>
        <testCase name="ThreeDotsMenu applies custom className" duration="2"/>
        <testCase name="ThreeDotsMenu triggers onClick when clicked" duration="2"/>
    </file>
    <file path="/Users/<USER>/Desktop/RA/paris2-web-risk-assessment/__tests__/context/DataStoreProvider.test.tsx">
        <testCase name="DataStoreProvider provides context and children" duration="8"/>
        <testCase name="DataStoreProvider allows updating dataStore via setDataStore" duration="4"/>
        <testCase name="DataStoreProvider navigates to /home if hasPermision is false" duration="2"/>
        <testCase name="DataStoreProvider throws if used outside provider" duration="14"/>
        <testCase name="DataStoreProvider memoizes context value" duration="3"/>
    </file>
</testExecutions>